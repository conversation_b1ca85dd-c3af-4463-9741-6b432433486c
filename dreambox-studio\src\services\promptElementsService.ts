import { supabase } from 'src/boot/supabase';
import { useAuthStore } from 'src/stores/auth';
import { useElementTypeOrderService } from 'src/services/elementTypeOrderService';
import { ref, computed } from 'vue';

// Types
export interface PromptElementType {
  id: number;
  name: string;
  description: string | null;
  is_array: boolean;
  is_required: boolean;
}

export interface PromptElement {
  id: number;
  type_id: number;
  value: string;
  description: string | null;
  is_system: boolean;
  designer_id: number | null;
  created_at: string;
  updated_at: string;
}

export interface PromptElementInput {
  type_id: number;
  value: string;
  description?: string | null;
  is_system?: boolean;
}

export interface PromptElementUsage {
  id: number;
  template_id: number;
  element_id: number;
  order: number;
  created_at: string;
}

export interface PromptElementOrder {
  elementId: number;
  order: number;
}

export interface OrderedPromptElement extends PromptElement {
  order_index: number;
}

/**
 * Service for managing prompt elements
 */
export const usePromptElementsService = () => {
  const loading = ref(false);
  const error = ref<string | null>(null);
  const authStore = useAuthStore();

  // Current user ID from auth store (app_user.id, not auth.user.id)
  const currentUserId = computed(() => {
    // We need the app_user.id (integer) from the auth store, not the auth.user.id (UUID)
    return authStore.state.user?.appUserId || null;
  });

  /**
   * Get all prompt element types
   */
  const getPromptElementTypes = async (): Promise<PromptElementType[]> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .from('prompt_element_types')
        .select('*')
        .order('name');

      if (err) {
        throw err;
      }

      // Transform the data to match the PromptElementType interface
      const transformedData = data?.map((item: Record<string, unknown>) => {
        // Access properties directly without unnecessary casting
        const typedItem = item;
        return {
          id: Number(typedItem.id),
          name: typeof typedItem.name === 'string' ? typedItem.name :
                typeof typedItem.name === 'number' ? String(typedItem.name) : '',
          description: typeof typedItem.description === 'string' ? typedItem.description :
                       typeof typedItem.description === 'number' ? String(typedItem.description) : null,
          is_array: Boolean(typedItem.is_array ?? true),
          is_required: Boolean(typedItem.is_required ?? false)
        };
      }) || [];

      return transformedData;
    } catch (err) {
      console.error('Error fetching prompt element types:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch prompt element types';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get all prompt elements with optional type filter
   */
  const getPromptElements = async (typeId?: number): Promise<PromptElement[]> => {
    try {
      loading.value = true;
      error.value = null;

      let query = supabase
        .from('prompt_elements')
        .select('*');

      // Apply type filter if provided
      if (typeId) {
        query = query.eq('type_id', typeId);
      }

      const { data, error: err } = await query.order('value');

      if (err) {
        throw err;
      }

      // Transform the data to match the PromptElement interface
      const transformedData = data?.map((item: Record<string, unknown>) => {
        // Access properties directly without unnecessary casting
        const typedItem = item;
        return {
          id: Number(typedItem.id),
          type_id: Number(typedItem.type_id),
          value: typeof typedItem.value === 'string' ? typedItem.value :
                 typeof typedItem.value === 'number' ? String(typedItem.value) : '',
          description: typeof typedItem.description === 'string' ? typedItem.description :
                       typeof typedItem.description === 'number' ? String(typedItem.description) : null,
          is_system: Boolean(typedItem.is_system ?? false),
          designer_id: typedItem.designer_id ? Number(typedItem.designer_id) : null,
          created_at: typeof typedItem.created_at === 'string' ? typedItem.created_at :
                      typeof typedItem.created_at === 'number' ? String(typedItem.created_at) : '',
          updated_at: typeof typedItem.updated_at === 'string' ? typedItem.updated_at :
                      typeof typedItem.updated_at === 'number' ? String(typedItem.updated_at) : ''
        };
      }) || [];

      return transformedData;
    } catch (err) {
      console.error('Error fetching prompt elements:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch prompt elements';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get prompt elements visible to current user (system + user's custom)
   * If userId is not provided, uses the current authenticated user
   */
  const getVisiblePromptElements = async (typeId?: number, userId?: number): Promise<PromptElement[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Use provided userId or try to get it directly from Supabase
      let userIdToUse = userId;

      if (!userIdToUse) {
        // Try to get the app_user.id for the current authenticated user
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            const { data: userData, error: userError } = await supabase
              .from('app_users')
              .select('id')
              .eq('user_id', user.id)
              .single();

            if (!userError && userData) {
              userIdToUse = userData.id;
              // console.log('Got app_user.id for current user:', userIdToUse);
            } else {
              console.error('Error getting app_user.id for current user:', userError);
            }
          }
        } catch (queryError) {
          console.error('Exception querying app_user.id:', queryError);
        }
      }

      // If we still don't have a user ID, fall back to getting all elements
      if (!userIdToUse) {
        console.warn('No user ID available, falling back to getPromptElements');
        return getPromptElements(typeId);
      }

      let query = supabase
        .from('prompt_elements')
        .select('*')
        .or(`is_system.eq.true,designer_id.eq.${userIdToUse}`);

      // Apply type filter if provided
      if (typeId) {
        query = query.eq('type_id', typeId);
      }

      const { data, error: err } = await query.order('value');

      if (err) {
        throw err;
      }

      // Transform the data to match the PromptElement interface
      const transformedData = data?.map((item: Record<string, unknown>) => {
        // Access properties directly without unnecessary casting
        const typedItem = item;
        return {
          id: Number(typedItem.id),
          type_id: Number(typedItem.type_id),
          value: typeof typedItem.value === 'string' ? typedItem.value :
                 typeof typedItem.value === 'number' ? String(typedItem.value) : '',
          description: typeof typedItem.description === 'string' ? typedItem.description :
                       typeof typedItem.description === 'number' ? String(typedItem.description) : null,
          is_system: Boolean(typedItem.is_system ?? false),
          designer_id: typedItem.designer_id ? Number(typedItem.designer_id) : null,
          created_at: typeof typedItem.created_at === 'string' ? typedItem.created_at :
                      typeof typedItem.created_at === 'number' ? String(typedItem.created_at) : '',
          updated_at: typeof typedItem.updated_at === 'string' ? typedItem.updated_at :
                      typeof typedItem.updated_at === 'number' ? String(typedItem.updated_at) : ''
        };
      }) || [];

      return transformedData;
    } catch (err) {
      console.error('Error fetching visible prompt elements:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch visible prompt elements';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Create new custom prompt element
   */
  const createCustomPromptElement = async (element: PromptElementInput): Promise<PromptElement | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Try to get the app_user.id directly from Supabase if not available from auth store
      let userIdToUse = currentUserId.value;

      if (!userIdToUse) {
        // Try to get the app_user.id directly from Supabase
        try {
          // Get the auth user ID first
          const { data: authData } = await supabase.auth.getUser();
          if (authData && authData.user) {
            // Now get the app_user.id using the auth user ID
            const { data: userData, error: userError } = await supabase
              .from('app_users')
              .select('id')
              .eq('user_id', authData.user.id)
              .single();

            if (!userError && userData) {
              userIdToUse = userData.id;
              // console.log('Got app_user.id directly from Supabase query for custom element:', userIdToUse);
            } else {
              console.error('Error getting app_user.id from query for custom element:', userError);
            }
          } else {
            console.error('No authenticated user found');
          }
        } catch (queryError) {
          console.error('Exception querying app_user.id for custom element:', queryError);
        }
      }

      // Ensure the current user ID is available
      if (!userIdToUse) {
        throw new Error('User must be authenticated to create custom prompt elements');
      }

      const { data, error: err } = await supabase
        .from('prompt_elements')
        .insert({
          type_id: element.type_id,
          value: element.value,
          description: element.description || null,
          is_system: false, // Custom elements are never system elements
          designer_id: userIdToUse
        })
        .select()
        .single();

      if (err) {
        throw err;
      }

      if (!data) return null;

      // Transform the data to match the PromptElement interface
      // Cast to a more specific type to access properties that might not be in the type
      const typedData = data as Record<string, unknown>;
      return {
        id: Number(typedData.id),
        type_id: Number(typedData.type_id),
        value: typeof typedData.value === 'string' ? typedData.value :
               typeof typedData.value === 'number' ? String(typedData.value) : '',
        description: typeof typedData.description === 'string' ? typedData.description :
                     typeof typedData.description === 'number' ? String(typedData.description) : null,
        is_system: Boolean(typedData.is_system ?? false),
        designer_id: typedData.designer_id ? Number(typedData.designer_id) : null,
        created_at: typeof typedData.created_at === 'string' ? typedData.created_at :
                    typeof typedData.created_at === 'number' ? String(typedData.created_at) : '',
        updated_at: typeof typedData.updated_at === 'string' ? typedData.updated_at :
                    typeof typedData.updated_at === 'number' ? String(typedData.updated_at) : ''
      };
    } catch (err) {
      console.error('Error creating custom prompt element:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create custom prompt element';
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Update existing custom prompt element
   * Note: Only the element's creator or an admin can update it
   */
  const updateCustomPromptElement = async (id: number, element: PromptElementInput): Promise<PromptElement | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Ensure the current user ID is available
      if (!currentUserId.value) {
        throw new Error('User must be authenticated to update custom prompt elements');
      }

      const { data, error: err } = await supabase
        .from('prompt_elements')
        .update({
          value: element.value,
          description: element.description || null
        })
        .eq('id', id)
        .select()
        .single();

      if (err) {
        throw err;
      }

      if (!data) return null;

      // Transform the data to match the PromptElement interface
      // Cast to a more specific type to access properties that might not be in the type
      const typedData = data as Record<string, unknown>;
      return {
        id: Number(typedData.id),
        type_id: Number(typedData.type_id),
        value: typeof typedData.value === 'string' ? typedData.value :
               typeof typedData.value === 'number' ? String(typedData.value) : '',
        description: typeof typedData.description === 'string' ? typedData.description :
                     typeof typedData.description === 'number' ? String(typedData.description) : null,
        is_system: Boolean(typedData.is_system ?? false),
        designer_id: typedData.designer_id ? Number(typedData.designer_id) : null,
        created_at: typeof typedData.created_at === 'string' ? typedData.created_at :
                    typeof typedData.created_at === 'number' ? String(typedData.created_at) : '',
        updated_at: typeof typedData.updated_at === 'string' ? typedData.updated_at :
                    typeof typedData.updated_at === 'number' ? String(typedData.updated_at) : ''
      };
    } catch (err) {
      console.error('Error updating custom prompt element:', err);
      error.value = err instanceof Error ? err.message : 'Failed to update custom prompt element';
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Delete custom prompt element
   * Note: Only the element's creator or an admin can delete it
   * Also, elements used in templates cannot be deleted
   */
  const deleteCustomPromptElement = async (id: number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Check if the element is used in any template
      const { data: usageData, error: usageError } = await supabase
        .from('prompt_elements_usage')
        .select('id')
        .eq('element_id', id)
        .limit(1);

      if (usageError) {
        throw usageError;
      }

      // If the element is used in templates, don't allow deletion
      if (usageData && usageData.length > 0) {
        throw new Error('Cannot delete a prompt element that is used in templates');
      }

      // Delete the element
      const { error: err } = await supabase
        .from('prompt_elements')
        .delete()
        .eq('id', id);

      if (err) {
        throw err;
      }

      return true;
    } catch (err) {
      console.error('Error deleting custom prompt element:', err);
      error.value = err instanceof Error ? err.message : 'Failed to delete custom prompt element';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get all elements for a template
   */
  const getTemplateElements = async (templateId: number): Promise<PromptElement[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Use a direct query to the database view to avoid ambiguous column references
      const { data, error: err } = await supabase
        .from('prompt_elements_usage_view')
        .select('*')
        .eq('template_id', templateId)
        .order('order', { ascending: true });

      if (err) {
        throw err;
      }

      // Transform the data from the view to our expected structure
      const elements = data?.map((item: Record<string, unknown>) => {
        // First check if item is valid
        if (!item || typeof item !== 'object') {
          console.error('Invalid item in data:', item);
          return null; // Skip this item
        }

        // Cast to more specific types to access properties
        const row = item as unknown as Record<string, unknown>;

        // Define the element with the correct type structure
        const element: PromptElement & {
          usage_id: number;
          order: number;
          type_name: string | undefined;
          type_description: string | null;
        } = {
          // Element properties
          id: Number(row.element_id),
          type_id: Number(row.pe_type_id),
          value: typeof row.value === 'string' ? row.value :
                 typeof row.value === 'number' ? String(row.value) : '',
          description: typeof row.element_description === 'string' ? row.element_description :
                       typeof row.element_description === 'number' ? String(row.element_description) : null,
          is_system: Boolean(row.is_system ?? false),
          designer_id: row.designer_id ? Number(row.designer_id) : null,
          created_at: typeof row.element_created_at === 'string' ? row.element_created_at :
                      typeof row.element_created_at === 'number' ? String(row.element_created_at) : '',
          updated_at: typeof row.element_updated_at === 'string' ? row.element_updated_at :
                      typeof row.element_updated_at === 'number' ? String(row.element_updated_at) : '',
          // Usage properties
          usage_id: Number(row.id),
          order: Number(row.order || 0),
          // Type properties
          type_name: typeof row.type_name === 'string' ? row.type_name :
                     typeof row.type_name === 'number' ? String(row.type_name) : undefined,
          type_description: typeof row.type_description === 'string' ? row.type_description :
                           typeof row.type_description === 'number' ? String(row.type_description) : null
        };

        return element;
      }).filter(Boolean) as PromptElement[]; // Filter out null values

      // Return the elements with the additional properties
      return elements as (PromptElement & {
        usage_id: number;
        order: number;
        type_name?: string;
        type_description?: string | null;
      })[];
    } catch (err) {
      console.error('Error fetching template elements:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch template elements';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Add element to template using prompt_elements_usage table
   */
  const addElementToTemplate = async (templateId: number, elementId: number, order?: number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Ensure templateId and elementId are valid numbers
      if (!templateId || isNaN(Number(templateId))) {
        throw new Error(`Invalid template ID: ${templateId}`);
      }

      if (!elementId || isNaN(Number(elementId))) {
        throw new Error(`Invalid element ID: ${elementId}`);
      }

      // Convert to numbers
      const numericTemplateId = Number(templateId);
      const numericElementId = Number(elementId);

      // console.log(`Adding element ${numericElementId} to template ${numericTemplateId} with order ${order}`);

      // First, check if the element already exists in this template
      const { data: existingData, error: existingErr } = await supabase
        .from('prompt_elements_usage')
        .select('id, order')
        .eq('template_id', numericTemplateId)
        .eq('element_id', numericElementId);

      if (existingErr) {
        console.error('Error checking if element exists:', existingErr);
        throw existingErr;
      }

      // console.log('Existing data check result:', existingData);

      // If the element already exists, we can skip the insert
      if (existingData && existingData.length > 0) {
        // console.log('Element already exists in template, skipping insert');
        return true;
      }

      // Get the element type for logging purposes
      const { data: elementData, error: elementErr } = await supabase
        .from('prompt_elements')
        .select('type_id, value')
        .eq('id', numericElementId)
        .single();

      if (elementErr) {
        console.error('Error getting element data:', elementErr);
        throw elementErr;
      }

      console.log(`Element ${numericElementId} has type ID ${elementData.type_id} and value "${elementData.value}"`);

      // Determine the order to use
      let finalOrder = order;
      if (finalOrder === undefined) {
        // Get the highest order for this template
        const { data: maxOrderData, error: maxOrderErr } = await supabase
          .from('prompt_elements_usage')
          .select('order')
          .eq('template_id', numericTemplateId)
          .order('order', { ascending: false })
          .limit(1);

        if (maxOrderErr) {
          console.error('Error getting max order:', maxOrderErr);
          throw maxOrderErr;
        }

        // Use the next available order
        finalOrder = maxOrderData && maxOrderData.length > 0 && maxOrderData[0]?.order !== undefined
          ? (maxOrderData[0].order + 1)
          : 1;
        // console.log(`Using next available order: ${finalOrder}`);
      }

      // Use the admin_insert_prompt_element_usage function which handles ordering within types
      // console.log(`Calling admin_insert_prompt_element_usage with params:`, {
      //   p_template_id: numericTemplateId,
      //   p_element_id: numericElementId,
      //   p_order: finalOrder !== undefined ? Number(finalOrder) : null
      // });

      const { data: wasInserted, error: insertErr } = await supabase.rpc('admin_insert_prompt_element_usage', {
        p_template_id: numericTemplateId,
        p_element_id: numericElementId,
        p_order: finalOrder !== undefined ? Number(finalOrder) : null
      });

      if (insertErr) {
        console.error('Error inserting element:', insertErr);
        throw insertErr;
      }

      // console.log('RPC result:', wasInserted);

      // If wasInserted is false, the element already existed
      if (wasInserted === false) {
        console.log('Element already exists in template according to RPC, skipping insert');
      } else {
        console.log('Successfully added element to template');
      }

      // Verify the element was added
      const { data: verifyData, error: verifyErr } = await supabase
        .from('prompt_elements_usage')
        .select('id, order')
        .eq('template_id', numericTemplateId)
        .eq('element_id', numericElementId);

      if (verifyErr) {
        console.error('Error verifying element insertion:', verifyErr);
        throw verifyErr;
      }

      // console.log('Verification result:', verifyData);

      if (!verifyData || verifyData.length === 0) {
        console.error('Element was not added to template!');

        // Try a direct insert as a fallback
        // console.log('Trying direct insert as fallback');

        const { data: directData, error: directErr } = await supabase
          .from('prompt_elements_usage')
          .insert({
            template_id: numericTemplateId,
            element_id: numericElementId,
            order: finalOrder || 1
          })
          .select();

        if (directErr) {
          console.error('Error with direct insert:', directErr);
          throw directErr;
        }

        console.log('Direct insert result:', directData);
      }

      return true;
    } catch (err) {
      console.error('Error adding element to template:', err);
      error.value = err instanceof Error ? err.message : 'Failed to add element to template';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Remove element from template using prompt_elements_usage table
   */
  const removeElementFromTemplate = async (templateId: number, elementId: number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Ensure templateId and elementId are valid numbers
      if (!templateId || isNaN(Number(templateId))) {
        throw new Error(`Invalid template ID: ${templateId}`);
      }

      if (!elementId || isNaN(Number(elementId))) {
        throw new Error(`Invalid element ID: ${elementId}`);
      }

      // Convert to numbers
      const numericTemplateId = Number(templateId);
      const numericElementId = Number(elementId);

      console.log('Removing element from template:', { templateId: numericTemplateId, elementId: numericElementId });

      // Delete the element from prompt_elements_usage
      const { error: deleteErr } = await supabase
        .from('prompt_elements_usage')
        .delete()
        .eq('template_id', numericTemplateId)
        .eq('element_id', numericElementId);

      if (deleteErr) {
        console.error('Error deleting element:', deleteErr);
        throw deleteErr;
      }

      // console.log('Successfully removed element from template');
      return true;
    } catch (err) {
      console.error('Error removing element from template:', err);
      error.value = err instanceof Error ? err.message : 'Failed to remove element from template';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Reorder elements in template using a SQL function to avoid ambiguous column references
   */
  const reorderTemplateElements = async (templateId: number, elementOrders: PromptElementOrder[]): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Use our SQL function to update all orders at once
      const { error: err } = await supabase.rpc('admin_reorder_template_elements', {
        p_template_id: templateId,
        p_element_orders: elementOrders
      });

      if (err) {
        console.error('Error reordering elements:', err);
        throw err;
      }

      return true;
    } catch (err) {
      console.error('Error reordering template elements:', err);
      error.value = err instanceof Error ? err.message : 'Failed to reorder template elements';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get ordered values for an element type
   */
  const getOrderedElementValues = async (templateId: number, elementTypeId: number): Promise<OrderedPromptElement[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Use two separate queries instead of a join to avoid TypeScript errors
      // First, get the ordered element IDs and their order indices
      const { data: orderData, error: orderErr } = await supabase
        .from('prompt_element_value_order')
        .select('element_id, order_index')
        .eq('template_id', templateId)
        .eq('element_type_id', elementTypeId)
        .order('order_index');

      if (orderErr) {
        throw orderErr;
      }

      // If we have order data, fetch the corresponding elements
      if (!orderData || orderData.length === 0) {
        return [];
      }

      // Get the element IDs from the order data
      const elementIds = orderData.map((item: { element_id: number }) => item.element_id);

      // Fetch the elements by their IDs
      const { data, error: err } = await supabase
        .from('prompt_elements')
        .select('id, type_id, value, description, is_system, designer_id')
        .in('id', elementIds);

      if (err) {
        throw err;
      }

      // If we don't have element data, return an empty array
      if (!data || data.length === 0) {
        return [];
      }

      // Create a map of element ID to element data for quick lookup
      const elementsMap = new Map();

      // Use a safer approach with unknown first
      const safeData = data as unknown as Array<{
        id: number;
        type_id: number;
        value: string;
        description: string | null;
        is_system: boolean;
        designer_id: number | null;
      }>;

      // Handle potential errors by checking properties before using them
      safeData.forEach(element => {
        if (element && typeof element === 'object' && 'id' in element) {
          elementsMap.set(element.id, element);
        }
      });

      // Transform the data to our expected structure
      // Use the order from orderData and the element details from data
      const elements = orderData.map((orderItem) => {
        const element = elementsMap.get(orderItem.element_id);

        // If we can't find the element, skip it
        if (!element) {
          return null;
        }

        // Safely access properties with defaults if they don't exist
        return {
          id: Number(element.id || 0),
          type_id: elementTypeId,
          value: typeof element.value === 'string' ? element.value : '',
          description: typeof element.description === 'string' ? element.description : null,
          is_system: Boolean(element.is_system),
          designer_id: typeof element.designer_id === 'number' ? element.designer_id : null,
          created_at: '',  // These fields are required by the interface but not used for ordering
          updated_at: '',  // We could fetch them if needed
          // Add the order_index as a custom property
          order_index: orderItem.order_index || 0 // Ensure order_index is always a number
        };
      })
      .filter((element): element is OrderedPromptElement => element !== null);

      return elements;
    } catch (err) {
      console.error('Error fetching ordered element values:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch ordered element values';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Reorder values within an element type
   */
  const reorderElementValues = async (
    templateId: number,
    elementTypeId: number,
    valueOrders: { elementId: number; order: number }[]
  ): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Use direct updates instead of RPC function to avoid ambiguous column references
      let hasError = false;

      // Update each value's order one by one
      for (const order of valueOrders) {
        const { error } = await supabase
          .from('prompt_element_value_order')
          .update({ order_index: order.order })
          .eq('template_id', templateId)
          .eq('element_type_id', elementTypeId)
          .eq('element_id', order.elementId);

        if (error) {
          console.error('Error updating value order:', error);
          hasError = true;
        }
      }

      // If any update failed, throw an error
      if (hasError) {
        throw new Error('Failed to update one or more value orders');
      }

      return true;
    } catch (err) {
      console.error('Error reordering element values:', err);
      error.value = err instanceof Error ? err.message : 'Failed to reorder element values';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get template elements with their types in a single query
   * This is an optimized version that reduces the number of database calls
   */
  const getTemplateElementsWithTypes = async (templateId: number): Promise<{
    types: Array<{
      id: number;
      name: string;
      description: string | null;
      order: number;
      elements: Array<{
        id: number;
        value: string;
        description: string | null;
        order: number;
      }>;
    }>;
  }> => {
    try {
      loading.value = true;
      error.value = null;

      // console.log(`Getting template elements with types for template ID ${templateId}`);

      // Use the elementTypeOrderService to get ordered types
      const elementTypeOrderService = useElementTypeOrderService();
      const orderedTypes = await elementTypeOrderService.getOrderedElementTypes(templateId);

      // console.log('Ordered types:', orderedTypes);

      // Process the result into a more usable structure
      const typesMap = new Map<number, {
        id: number;
        name: string;
        description: string | null;
        order: number;
        elements: Array<{
          id: number;
          value: string;
          description: string | null;
          order: number;
        }>;
      }>();

      // Get details for each type
      for (const typeOrder of orderedTypes || []) {
        const typeId = typeOrder.element_type_id;

        // Get type details
        const { data: typeData, error: typeErr } = await supabase
          .from('prompt_element_types')
          .select('*')
          .eq('id', typeId)
          .single();

        if (typeErr) {
          console.error(`Error getting type ${typeId}:`, typeErr);
          continue;
        }

        // Add the type to the map
        typesMap.set(typeId, {
          id: typeId,
          name: typeData.name,
          description: typeData.description,
          order: typeOrder.order_index,
          elements: []
        });
      }

      // console.log('Types map after loading types:', typesMap);

      // Now get all elements for this template from prompt_elements_usage
      const { data: usageData, error: usageErr } = await supabase
        .from('prompt_elements_usage')
        .select('*')
        .eq('template_id', templateId);

      if (usageErr) {
        console.error('Error getting prompt_elements_usage:', usageErr);
        throw usageErr;
      }

      // console.log('Usage data:', usageData);

      // Get all value orders for this template
      const { data: valueOrderData, error: valueOrderErr } = await supabase
        .from('prompt_element_value_order')
        .select('*')
        .eq('template_id', templateId);

      if (valueOrderErr) {
        console.error('Error getting prompt_element_value_order:', valueOrderErr);
        // Don't throw, we can continue without order data
      }

      // console.log('Value order data:', valueOrderData);

      // Create a map of element ID to order index for quick lookup
      const elementOrderMap = new Map<number, number>();
      if (valueOrderData && valueOrderData.length > 0) {
        valueOrderData.forEach(order => {
          elementOrderMap.set(order.element_id, order.order_index);
        });
      }

      // Get details for each element
      for (const usage of usageData || []) {
        // Get element details
        const { data: elementData, error: elementErr } = await supabase
          .from('prompt_elements')
          .select('*')
          .eq('id', usage.element_id)
          .single();

        if (elementErr) {
          console.error(`Error getting element ${usage.element_id}:`, elementErr);
          continue;
        }

        // Get the type for this element
        const typeId = elementData.type_id;

        // Get or create the type object
        let typeObj = typesMap.get(typeId);
        if (!typeObj) {
          // If the type isn't in our map yet, get its details
          const { data: typeData, error: typeErr } = await supabase
            .from('prompt_element_types')
            .select('*')
            .eq('id', typeId)
            .single();

          if (typeErr) {
            console.error(`Error getting type ${typeId}:`, typeErr);
            continue;
          }

          // Add the type to the map
          typeObj = {
            id: typeId,
            name: typeData.name,
            description: typeData.description,
            order: typesMap.size, // Use the current size as the order
            elements: []
          };
          typesMap.set(typeId, typeObj);
        }

        // Determine the order - use value_order if available, otherwise use usage.order
        let orderValue = usage.order;
        if (elementOrderMap.has(elementData.id)) {
          orderValue = elementOrderMap.get(elementData.id) || 0;
        }

        // Add the element to the type
        typeObj.elements.push({
          id: elementData.id,
          value: elementData.value,
          description: elementData.description,
          order: orderValue
        });
      }

      // console.log('Types map after loading elements:', typesMap);

      // Convert the map to an array and sort by order
      const types = Array.from(typesMap.values()).sort((a, b) => a.order - b.order);

      // Sort elements within each type by order
      types.forEach(type => {
        type.elements.sort((a, b) => a.order - b.order);
      });

      // console.log('Final types array:', types);

      return { types };
    } catch (err) {
      console.error('Error fetching template elements with types:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch template elements with types';
      return { types: [] };
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    getPromptElementTypes,
    getPromptElements,
    getVisiblePromptElements,
    createCustomPromptElement,
    updateCustomPromptElement,
    deleteCustomPromptElement,
    getTemplateElements,
    addElementToTemplate,
    removeElementFromTemplate,
    reorderTemplateElements,
    getOrderedElementValues,
    reorderElementValues,
    getTemplateElementsWithTypes
  };
};
