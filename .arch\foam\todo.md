- [ ] check for memory leaks
- [ ] prompt engeneering for augment
- [ ] project overview
- [ ] database overview
- [ ] database slow queries

Image generation: 
1.) spin server (pod) - button on top bar
2.) notify user, that server is ready for image generation
2.) generate images - template, product, marketing, etc.
3.) save images to s3, save metadata to supabase (s3_images)
4.) display image in preview tab, option to delete image on s3/supabase
5.) shut down server - button on top bar


Chat: 
- [ ] commands in chat history should be executable
- [ ] chat history should be saved to supabase so it can be loaded in new session
- [ ] add button to clear chat history

Perfect! Looks like everything is working fine.
I think we did almost all for this testing solution.
In the next step we need to think about pooling of comfyUI server for multiple users.
I think we could create table in supabasee with running servers, and when user logs into the app, we check if there is any available server, and if not, he can create new one. This is one thing.
Another thing is case of mass image production. In that canse we need to think about pooling multiple comfyUI servers, or maybe choose more powerful GPU, depends what is cheaper.

Then, if something goes wrong... Is it possible to set something on runpod so that it automatically terminates server after some time of inactivity?
If not, we can implement this on the client in our app. Maybe we can also add some settings for that, like terminate server after 10/20/30 minutes of inactivity, or 1 hour, or 2 hours, or never.

What do you think about this? No code for now, just some thoughts...

