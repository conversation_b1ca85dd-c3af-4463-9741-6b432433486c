/**
 * Component Registry
 *
 * Registers and retrieves UI components for the Context Manager.
 * Ensures that components are only loaded when needed.
 */
// console.log('Loading componentRegistry.ts');

import { boot } from 'quasar/wrappers';
import { markRaw, defineAsyncComponent } from 'vue';
import type { Component } from 'vue';

// Component registry
const componentRegistry: Record<string, Component> = {};

/**
 * Register a component
 *
 * @param name The name of the component
 * @param component The component to register
 */
export function registerComponent(name: string, component: Component): void {
  componentRegistry[name] = markRaw(component);
}

/**
 * Register an async component
 *
 * @param name The name of the component
 * @param importFn The import function for the component
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function registerAsyncComponent(name: string, importFn: () => Promise<any>): void {
  componentRegistry[name] = markRaw(defineAsyncComponent(importFn));
}

/**
 * Get a component by name
 *
 * @param name The name of the component
 * @returns The component or undefined if not found
 */
export function getComponent(name: string): Component | undefined {
  return componentRegistry[name];
}

// Register core components
export default boot(({ app }) => {
  console.log('Initializing component registry...');

  // Navigation components
  registerAsyncComponent('Navigation', () => import('src/components/navigation/TreeNavigation.vue'));

  // Drawer components
  registerAsyncComponent('FilterPanel', () => import('src/lib/filters/components/FilterPanel.vue'));
  registerAsyncComponent('TableFilterPanel', () => import('src/components/filters/TableFilterPanel.vue'));
  registerAsyncComponent('PromptElementsPanel', () => import('src/components/templates/TemplateBuilderTools.vue'));
  registerAsyncComponent('ElementValuesEditor', () => import('src/components/templates/TemplateBuilderSettings.vue'));
  registerAsyncComponent('ChatAssistant', () => import('src/components/chat/ChatAssistant.vue'));
  registerAsyncComponent('TemplatePreview', () => import('src/components/templates/TemplateBuilderPreview.vue'));

  // Top bar components
  registerAsyncComponent('SearchBar', () => import('src/components/search/SearchBar.vue'));
  registerAsyncComponent('ViewToggle', () => import('src/components/common/ViewToggle.vue'));

  // Bottom bar components
  registerAsyncComponent('ActionButton', () => import('src/components/common/ActionButton.vue'));
  registerAsyncComponent('ActionButtonsContainer', () => import('src/components/common/ActionButtonsContainer.vue'));
  registerAsyncComponent('ActionButtonsExpanded', () => import('src/components/common/ActionButtonsExpanded.vue'));

  // Log registered components
  console.log('Registered components:', Object.keys(componentRegistry));

  // Make componentRegistry available to all components
  app.config.globalProperties.$components = {
    get: getComponent,
    register: registerComponent,
    registerAsync: registerAsyncComponent
  };

  console.log('Component registry initialized');
});
