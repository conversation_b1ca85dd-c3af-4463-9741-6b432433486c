import { supabase } from 'src/boot/supabase';
import type {
  ComfyUIGenerationParams,
  ComfyUIGenerationResponse,
  ComfyUIGeneratedImage,
  ComfyUIModel,
  ComfyUIServerInfo,
  ServerStatus,
  ServerStartOptions,
  ServerStartResult
} from './types';

// Interface for s3_images table
interface S3ImageRecord {
  id?: number;
  user_id: number;
  s3_url: string;
  s3_key: string;
  image_type: string;
  width: number;
  height: number;
  prompt: string;
  negative_prompt: string;
  seed: number | null;
  model_used: string;
  created_at: string;
  updated_at: string;
}

class ComfyUIService {
  public statusCheckInterval: number | null = null;
  private serverStatus: ServerStatus = { status: 'no-server' };
  private readonly STORAGE_KEY = 'comfyui-pod-id';

  constructor() {
    // console.log('🚀 DEBUG: ComfyUIService constructor called at', new Date().toISOString());

    // Load stored podId on initialization
    this.loadStoredPodId();

    // Start checking server status periodically
    this.startStatusCheck();
  }

  /**
   * Load stored podId from localStorage
   */
  private loadStoredPodId(): void {
    try {
      const storedPodId = localStorage.getItem(this.STORAGE_KEY);
      if (storedPodId) {
        console.log('Found stored podId:', storedPodId);
        this.serverStatus.podId = storedPodId;
        this.serverStatus.serverId = storedPodId;
        this.serverStatus.apiEndpoint = `https://${storedPodId}-3000.proxy.runpod.net`;
      }
    } catch (error) {
      console.warn('Failed to load stored podId:', error);
    }
  }

  /**
   * Store podId in localStorage
   */
  private storePodId(podId: string): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, podId);
      // console.log('Stored podId in localStorage:', podId);
    } catch (error) {
      console.warn('Failed to store podId:', error);
    }
  }

  /**
   * Clear stored podId from localStorage
   */
  private clearStoredPodId(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      // console.log('🧹 DEBUG: Cleared stored podId from localStorage at', new Date().toISOString());
    } catch (error) {
      console.warn('Failed to clear stored podId:', error);
    }
  }

  /**
   * Start periodic server status check
   */
  startStatusCheck(intervalMs = 10000) {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }

    // Initial check
    // void this.checkServerStatus();

    // Set up interval for periodic checks
    this.statusCheckInterval = window.setInterval(() => {
      // void this.checkServerStatus();
    }, intervalMs);
  }

  /**
   * Stop periodic server status check
   */
  stopStatusCheck() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
      this.statusCheckInterval = null;
    }
  }

  /**
   * Check the current server status
   */
  async checkServerStatus(): Promise<ServerStatus> {
    try {
      // First, test authentication
      const { error: authError } = await supabase.functions.invoke('comfyui-server?action=test-auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      // Only log authentication errors, not successful responses
      if (authError) {
        console.error('Error testing authentication:', authError);
      }

      // Then check server status
      // Use stored podId if available
      const podId = this.serverStatus.podId;
      const queryParams = podId ? `&podId=${podId}` : '';

      const { data, error } = await supabase.functions.invoke(`comfyui-server?action=server-status${queryParams}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      // console.log('📊 DEBUG: Server status response at', new Date().toISOString(), ':', data);

      if (error) {
        console.error('Error checking server status:', error);
        this.serverStatus = {
          status: 'error',
          message: error.message
        };
        return this.serverStatus;
      }

      if (!data) {
        this.serverStatus = { status: 'no-server' };
        return this.serverStatus;
      }

      // If the status is 'no-server', clear the stored podId
      if (data.status === 'no-server') {
        this.clearStoredPodId();
        this.serverStatus = { status: 'no-server' };
        return this.serverStatus;
      }

      // Update server status with all available information
      this.serverStatus = {
        ...data,
        // Ensure we have the API endpoint if we have a podId
        apiEndpoint: data.apiEndpoint || (data.podId ? `https://${data.podId}-3000.proxy.runpod.net` : undefined)
      };

      // console.log('Updated server status:', this.serverStatus);
      return this.serverStatus;
    } catch (error) {
      console.error('Error checking server status:', error);
      this.serverStatus = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
      return this.serverStatus;
    }
  }

  /**
   * Get the current server status without making an API call
   */
  getCurrentStatus(): ServerStatus {
    return this.serverStatus;
  }

  /**
   * Test RunPod API
   */
  async testRunPodAPI(): Promise<boolean> {
    // Skip API tests in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Skipping RunPod API test in development mode');
      return true;
    }

    try {
      // Testing RunPod API
      const { data, error } = await supabase.functions.invoke('comfyui-server?action=test-runpod', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error testing RunPod API:', error);
        return false;
      }

      return data.status === 'success';
    } catch (error) {
      console.error('Error testing RunPod API:', error);
      return false;
    }
  }

  /**
   * Test RunPod pod creation
   */
  async testCreatePod(options: ServerStartOptions = {}): Promise<boolean> {
    // Skip API tests in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Skipping RunPod pod creation test in development mode');
      return true;
    }

    try {
      // Testing RunPod pod creation
      const { data, error } = await supabase.functions.invoke('comfyui-server?action=test-create-pod', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: { ...options }
      });

      if (error) {
        console.error('Error testing RunPod pod creation:', error);
        return false;
      }

      return data.status === 'success';
    } catch (error) {
      console.error('Error testing RunPod pod creation:', error);
      return false;
    }
  }

  /**
   * Start a ComfyUI server
   */
  async startServer(options: ServerStartOptions = {}): Promise<ServerStartResult> {
    try {
      // Skip API tests in development mode
      if (process.env.NODE_ENV !== 'development') {
        // First, test the RunPod API
        const apiWorking = await this.testRunPodAPI();
        if (!apiWorking) {
          return {
            status: 'error',
            message: 'RunPod API test failed. Check the logs for details.'
          };
        }

        // Then, test pod creation
        const podCreationWorking = await this.testCreatePod(options);
        if (!podCreationWorking) {
          return {
            status: 'error',
            message: 'RunPod pod creation test failed. Check the logs for details.'
          };
        }
      }

      // Call the edge function to start a server
      const { data, error } = await supabase.functions.invoke('comfyui-server?action=start-server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: { ...options }
      });

      // Log both success and error responses for debugging
      console.log('Server creation response:', data);

      if (error) {
        console.error('Error in pod creation response:', error);
        return {
          status: 'error',
          message: error.message
        };
      }

      // Additional validation
      if (!data) {
        console.error('Server creation response is empty');
        return {
          status: 'error',
          message: 'Server creation response is empty'
        };
      }

      // Check for pod ID or server ID
      if (!data.podId && !data.serverId) {
        console.error('Server creation response missing pod ID:', data);
        return {
          status: 'error',
          message: 'Server creation response missing pod ID'
        };
      }

      // Update the server status immediately with the new pod ID
      const podId = data.podId || data.serverId;
      this.serverStatus = {
        status: data.status || 'starting',
        podId: podId,
        serverId: podId,
        apiEndpoint: `https://${podId}-3000.proxy.runpod.net`,
        gpu_type: options.gpuTypeId || 'NVIDIA RTX A4000',
        started_at: new Date().toISOString()
      };

      // Store podId in localStorage for persistence across page reloads
      this.storePodId(podId);

      // Trigger an immediate status check after a short delay
      setTimeout(() => { void this.checkServerStatus(); }, 5000);

      return {
        serverId: data.podId || data.serverId,
        status: data.status || 'starting',
        message: data.message || 'Server is starting'
      };
    } catch (error) {
      console.error('Error starting server:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Stop a ComfyUI server (pause it)
   */
  async stopServer(): Promise<boolean> {
    if (!this.serverStatus.podId) {
      console.error('No active server to stop');
      return false;
    }

    try {
      const { error } = await supabase.functions.invoke('comfyui-server', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: {
          action: 'stop-server',
          podId: this.serverStatus.podId
        }
      });

      if (error) {
        console.error('Error stopping server:', error);
        return false;
      }

      // Trigger an immediate status check
      // setTimeout(() => { void this.checkServerStatus(); }, 2000);

      return true;
    } catch (error) {
      console.error('Error stopping server:', error);
      return false;
    }
  }

  /**
   * Terminate a ComfyUI server (completely delete it)
   */
  async terminateServer(): Promise<boolean> {
    if (!this.serverStatus.podId) {
      console.error('No active server to terminate');
      return false;
    }

    try {
      console.log(`Terminating pod ${this.serverStatus.podId}`);

      // Call the edge function with the terminate-server action
      const { data, error } = await supabase.functions.invoke(`comfyui-server?action=terminate-server&podId=${this.serverStatus.podId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      console.log('Terminate server response:', data);

      if (error) {
        console.error('Error terminating server:', error);
        return false;
      }

      // Clear stored podId and update local status
      this.clearStoredPodId();
      this.serverStatus = { status: 'no-server' };

      return true;
    } catch (error) {
      console.error('Error terminating server:', error);
      return false;
    }
  }

  /**
   * Get ComfyUI server info
   */
  async getServerInfo(): Promise<ComfyUIServerInfo | null> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return null;
    }

    // In development mode, return a simulated response
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning simulated server info in development mode');
      return {
        object_info: {
          CheckpointLoaderSimple: {
            input: {
              required: {
                ckpt_name: {
                  type: "COMBO",
                  options: ["flux1-schnell-fp8.safetensors"]
                }
              }
            },
            output: ["MODEL", "CLIP", "VAE"]
          }
        },
        extensions: ["core", "comfy"],
        system_stats: {
          cuda: {
            name: "NVIDIA RTX A4000",
            vram_total: 16384,
            vram_free: 14336
          }
        }
      };
    }

    try {
      const { data, error } = await supabase.functions.invoke('comfyui-generation?path=object_info', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error getting server info:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting server info:', error);
      return null;
    }
  }

  /**
   * Get available models
   */
  async getModels(): Promise<ComfyUIModel[]> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return [];
    }

    // In development mode, return a simulated response
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning simulated models in development mode');
      return [
        {
          id: 'flux1-schnell-fp8.safetensors',
          name: 'FLUX.1 Schnell',
          type: 'checkpoint'
        }
      ];
    }

    try {
      const { data, error } = await supabase.functions.invoke('comfyui-generation?path=object_info/CheckpointLoaderSimple', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error getting models:', error);
        return [];
      }

      // Transform ComfyUI model format to our format
      const models = Object.entries(data.input?.required?.ckpt_name?.options || {}).map(([id, name]) => ({
        id,
        name: String(name),
        type: 'checkpoint'
      }));

      return models;
    } catch (error) {
      console.error('Error getting models:', error);
      return [];
    }
  }

  /**
   * Generate an image using ComfyUI
   */
  async generateImage(params: ComfyUIGenerationParams): Promise<ComfyUIGenerationResponse | null> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return null;
    }

    try {
      console.log('Calling ComfyUI generation with params:', params);

      const { data, error } = await supabase.functions.invoke('comfyui-generation', {
        method: 'POST',
        body: {
          ...params,
          serverEndpoint: this.serverStatus.apiEndpoint
        }
      });

      if (error) {
        console.error('Error generating image:', error);
        return null;
      }

      console.log('ComfyUI generation response:', data);

      // If we have images, construct proper URLs and save them
      if (data && data.images && data.images.length > 0) {
        const processedImages = data.images.map((image: ComfyUIGeneratedImage) => ({
          ...image,
          url: `${this.serverStatus.apiEndpoint}/view?filename=${image.filename}&subfolder=${image.subfolder || ''}&type=${image.type || 'output'}`
        }));

        // Save the first image to storage (temporarily disabled for debugging)
        if (processedImages[0]) {
          try {
            console.log('Image saving temporarily disabled for debugging');
            // await this.saveComfyUIImage(processedImages[0], params);
          } catch (saveError) {
            console.warn('Failed to save generated image:', saveError);
            // Don't fail the whole generation if saving fails
          }
        }

        return {
          ...data,
          images: processedImages
        };
      }

      return data;
    } catch (error) {
      console.error('Error generating image:', error);
      return null;
    }
  }

  /**
   * Save a ComfyUI generated image to storage
   */
  private async saveComfyUIImage(image: ComfyUIGeneratedImage, params: ComfyUIGenerationParams): Promise<void> {
    try {
      // Extract metadata from the generation parameters
      const workflow = params.prompt;
      const metadata: Record<string, unknown> = {
        source: 'comfyui',
        model: 'FLUX',
        width: 1024,
        height: 1024
      };

      // Try to extract prompt and other parameters from the workflow
      if (workflow) {
        // Look for common node types that contain prompt information
        for (const [, node] of Object.entries(workflow)) {
          if (node.class_type === 'CLIPTextEncode' && node.inputs) {
            if (!metadata.prompt && typeof node.inputs.text === 'string') {
              metadata.prompt = node.inputs.text;
            }
          }
          if (node.class_type === 'EmptyLatentImage' && node.inputs) {
            metadata.width = node.inputs.width || metadata.width;
            metadata.height = node.inputs.height || metadata.height;
          }
          if (node.class_type === 'KSampler' && node.inputs) {
            metadata.steps = node.inputs.steps;
            metadata.cfg_scale = node.inputs.cfg;
            metadata.seed = node.inputs.seed;
            metadata.sampler = node.inputs.sampler_name;
          }
        }
      }

      // Call the image management edge function to save the image
      const { error } = await supabase.functions.invoke('image-management?action=save-comfyui-image', {
        method: 'POST',
        body: {
          imageUrl: image.url,
          metadata
        }
      });

      if (error) {
        console.error('Error saving image:', error);
      } else {
        console.log('Image saved successfully');
      }
    } catch (error) {
      console.error('Error in saveComfyUIImage:', error);
      throw error;
    }
  }

  /**
   * Save a generated image directly via edge function (bypasses client-side CORS issues)
   */
  async saveGeneratedImage(imageUrl: string, metadata: Record<string, unknown>): Promise<string> {
    console.log('Saving image via edge function:', imageUrl);

    // Get the current session for authentication
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('No active session - user not authenticated');
    }

    // Use direct fetch instead of supabase.functions.invoke to ensure body is sent correctly
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/image-management`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        action: 'save-comfyui-image',
        imageUrl,
        metadata
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Edge function error response:', errorText);
      throw new Error(`Failed to save image: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data?.success) {
      throw new Error(`Failed to save image: ${data?.error || 'Unknown error'}`);
    }

    return data.image.id;
  }

  /**
   * Fetch image with multiple fallback approaches to handle CORS issues
   */
  private async fetchImageWithFallbacks(imageUrl: string): Promise<Blob> {
    // Try different approaches in order of preference

    // Approach 1: Try no-cors mode (allows fetching but with opaque response)
    try {
      console.log('Trying no-cors fetch approach');
      const imageResponse = await fetch(imageUrl, {
        method: 'GET',
        mode: 'no-cors',
        cache: 'no-cache'
      });

      // With no-cors, we can't check response.ok, but we can try to get the blob
      const imageBlob = await imageResponse.blob();

      // Check if we got a valid blob (no-cors responses should still have size > 0 if successful)
      if (imageBlob && imageBlob.size > 0 && imageBlob.type) {
        console.log('Image fetched successfully with no-cors mode, size:', imageBlob.size, 'bytes, type:', imageBlob.type);
        return imageBlob;
      } else {
        console.log('No-cors fetch returned blob but with size:', imageBlob?.size, 'type:', imageBlob?.type);
        throw new Error('No-cors fetch returned invalid blob');
      }
    } catch (noCorsError) {
      console.log('No-cors fetch failed:', noCorsError);
    }

    // Approach 2: Try regular CORS fetch
    try {
      console.log('Trying regular CORS fetch');
      const imageResponse = await fetch(imageUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/*',
        },
        mode: 'cors'
      });

      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      const imageBlob = await imageResponse.blob();
      console.log('Image fetched successfully with CORS mode, size:', imageBlob.size, 'bytes');
      return imageBlob;
    } catch (corsError) {
      console.log('CORS fetch failed:', corsError);
    }

    // Approach 3: Use edge function as proxy (with authentication)
    try {
      console.log('Trying proxy approach via edge function');

      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session for proxy request');
      }

      // Use supabase.functions.invoke now that we have a clean edge function
      const { data: proxyData, error: proxyError } = await supabase.functions.invoke('image-management', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: {
          action: 'fetch-image',
          imageUrl
        }
      });

      console.log('Supabase functions invoke response:', { proxyData, proxyError });

      if (proxyError) {
        throw new Error(`Proxy fetch failed: ${proxyError.message || 'Unknown proxy error'}`);
      }

      if (!proxyData || !proxyData.success) {
        throw new Error(`Proxy fetch failed: ${proxyData?.error || 'Unknown proxy error'}`);
      }

      // Convert base64 data to blob if returned from proxy
      if (proxyData.imageData) {
        const binaryString = atob(proxyData.imageData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const imageBlob = new Blob([bytes], { type: 'image/png' });
        console.log('Image fetched successfully via proxy, size:', imageBlob.size, 'bytes');
        return imageBlob;
      } else {
        throw new Error('Proxy did not return image data');
      }
    } catch (proxyError) {
      console.log('Proxy approach failed:', proxyError);
    }

    // If all approaches fail, throw a comprehensive error
    throw new Error(`All image fetch approaches failed. The ComfyUI server at ${imageUrl} does not allow cross-origin requests, and the proxy approach also failed. This is a limitation of browser security policies when accessing external image servers.`);
  }

  /**
   * Public method to fetch an image as a blob (used for display)
   */
  async fetchImageAsBlob(imageUrl: string): Promise<Blob> {
    return this.fetchImageWithFallbacks(imageUrl);
  }

  /**
   * Test the edge function connectivity
   */
  async testEdgeFunction(): Promise<boolean> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('No active session for edge function test');
        return false;
      }

      const { data, error } = await supabase.functions.invoke('image-management?action=test', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (error) {
        console.error('Edge function test error:', error);
        return false;
      }

      console.log('Edge function test response:', data);
      return true;
    } catch (error) {
      console.error('Edge function test failed:', error);
      return false;
    }
  }

  /**
   * Check if ComfyUI server is ready for image generation
   */
  async checkComfyUIHealth(apiEndpoint: string): Promise<boolean> {
    try {
      console.log('Checking ComfyUI health via edge function for:', apiEndpoint);

      // Use edge function to avoid CORS issues
      const { data, error } = await supabase.functions.invoke('image-management', {
        body: {
          action: 'health-check',
          apiEndpoint: apiEndpoint
        }
      });

      if (error) {
        console.log('Edge function health check error:', error);
        return false;
      }

      if (data && data.success && data.healthy) {
        console.log('ComfyUI health check successful via edge function');
        return true;
      } else {
        console.log('ComfyUI health check failed via edge function:', data);
        return false;
      }
    } catch (error) {
      console.log('ComfyUI health check error:', error);
      return false;
    }
  }

  /**
   * Poll ComfyUI server until it's ready
   */
  async waitForComfyUIReady(apiEndpoint: string, maxWaitTime: number = 300000): Promise<boolean> {
    const startTime = Date.now();
    const pollInterval = 5000; // Check every 5 seconds

    console.log('Waiting for ComfyUI to be ready...');

    while (Date.now() - startTime < maxWaitTime) {
      const isReady = await this.checkComfyUIHealth(apiEndpoint);

      if (isReady) {
        console.log('ComfyUI is ready!');
        return true;
      }

      console.log('ComfyUI not ready yet, waiting...');
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    console.log('Timeout waiting for ComfyUI to be ready');
    return false;
  }

  /**
   * Generate a warm-up image to prepare ComfyUI for faster subsequent generations
   */
  async generateWarmupImage(apiEndpoint: string): Promise<boolean> {
    try {
      console.log('Generating warmup image...');

      // Use a simple, fast prompt for warmup
      const warmupPrompt = 'test';
      const warmupWorkflow = {
        "3": {
          "inputs": {
            "seed": 1,
            "steps": 1,
            "cfg": 1,
            "sampler_name": "euler",
            "scheduler": "simple",
            "denoise": 1,
            "model": ["4", 0],
            "positive": ["6", 0],
            "negative": ["7", 0],
            "latent_image": ["5", 0]
          },
          "class_type": "KSampler",
          "_meta": {"title": "KSampler"}
        },
        "4": {
          "inputs": {"ckpt_name": "flux1-dev.safetensors"},
          "class_type": "CheckpointLoaderSimple",
          "_meta": {"title": "Load Checkpoint"}
        },
        "5": {
          "inputs": {"width": 512, "height": 512, "batch_size": 1},
          "class_type": "EmptyLatentImage",
          "_meta": {"title": "Empty Latent Image"}
        },
        "6": {
          "inputs": {"text": warmupPrompt, "clip": ["4", 1]},
          "class_type": "CLIPTextEncode",
          "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "7": {
          "inputs": {"text": "", "clip": ["4", 1]},
          "class_type": "CLIPTextEncode",
          "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "8": {
          "inputs": {"samples": ["3", 0], "vae": ["4", 2]},
          "class_type": "VAEDecode",
          "_meta": {"title": "VAE Decode"}
        },
        "9": {
          "inputs": {"filename_prefix": "warmup", "images": ["8", 0]},
          "class_type": "SaveImage",
          "_meta": {"title": "Save Image"}
        }
      };

      const response = await fetch(`${apiEndpoint}/prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ prompt: warmupWorkflow })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Warmup image generation started:', result.prompt_id);

        // Wait for warmup to complete (don't need the actual image)
        await this.waitForWarmupCompletion(apiEndpoint, result.prompt_id);
        console.log('Warmup image generation completed');
        return true;
      } else {
        console.log('Warmup image generation failed:', response.status);
        return false;
      }
    } catch (error) {
      console.log('Warmup image generation error:', error);
      return false;
    }
  }

  /**
   * Wait for warmup image generation to complete
   */
  private async waitForWarmupCompletion(apiEndpoint: string, promptId: string): Promise<void> {
    const maxWaitTime = 60000; // 1 minute max for warmup
    const pollInterval = 1000; // Check every second
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(`${apiEndpoint}/history/${promptId}`);
        if (response.ok) {
          const history = await response.json();
          if (history[promptId] && history[promptId].status) {
            const status = history[promptId].status;
            if (status.completed) {
              return; // Warmup completed
            }
          }
        }
      } catch (error) {
        console.log('Error checking warmup status:', error);
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    console.log('Warmup completion check timed out');
  }

  /**
   * Save an image from an already-fetched blob
   */
  async saveGeneratedImageFromBlob(imageBlob: Blob, metadata: Record<string, unknown>): Promise<string | null> {
    try {
      console.log('Starting image save process from blob, size:', imageBlob.size, 'bytes');

      // Step 1: Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Step 2: Upload to Supabase storage
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const filename = `generated/${user.id}/${timestamp}_${randomString}.png`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filename, imageBlob, {
          contentType: 'image/png',
          cacheControl: '3600'
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      console.log('Image uploaded successfully to:', filename);

      // Step 3: Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filename);

      // Step 4: Get app_user_id
      const { data: appUser, error: appUserError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (appUserError || !appUser) {
        throw new Error('User not found in app_users table');
      }

      // Step 5: Save to s3_images table
      const imageRecord: Omit<S3ImageRecord, 'id'> = {
        user_id: appUser.id,
        s3_url: publicUrl,
        s3_key: filename,
        image_type: 'generated',
        width: Number(metadata?.width) || 1024,
        height: Number(metadata?.height) || 1024,
        prompt: typeof metadata?.prompt === 'string' ? metadata.prompt : '',
        negative_prompt: typeof metadata?.negative_prompt === 'string' ? metadata.negative_prompt : '',
        seed: metadata?.seed ? Number(metadata.seed) : null,
        model_used: typeof metadata?.model === 'string' ? metadata.model : 'ComfyUI',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Use a more specific type assertion for the database operation
      const supabaseClient = supabase as unknown as {
        from: (table: string) => {
          insert: (data: Omit<S3ImageRecord, 'id'>) => {
            select: () => {
              single: () => Promise<{ data: S3ImageRecord | null; error: { message: string } | null }>;
            };
          };
        };
      };

      const { data: imageData, error: imageError } = await supabaseClient
        .from('s3_images')
        .insert(imageRecord)
        .select()
        .single();

      if (imageError) {
        throw new Error(`Database save failed: ${imageError.message}`);
      }

      console.log('Image saved successfully with ID:', imageData?.id);
      return imageData?.id?.toString() || 'unknown';

    } catch (error) {
      console.error('Error saving generated image from blob:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const comfyUIService = new ComfyUIService();
