<template>
  <div class="prompt-elements-editor">
    <!-- Main prompt display area -->
    <div class="prompt-display">
      <!-- Selected types with values -->
      <draggable
        v-model="selectedElementTypes"
        item-key="id"
        handle=".type-drag-handle"
        @end="onTypeReorderEnd"
        class="types-container"
      >
        <template #item="{ element: elementType, index }">
        <!-- Type display -->
        <span
          class="element-type-container"
          @mouseenter="hoveredTypeIndex = index"
          @mouseleave="hoveredTypeIndex = -1"
        >
          <!-- Hover controls for type -->
          <div v-if="hoveredTypeIndex === index" class="hover-controls type-controls">
            <q-btn
              flat
              dense
              size="sm"
              icon="drag_indicator"
              class="type-drag-handle"
              title="Drag to reorder"
            />
            <q-btn
              flat
              dense
              size="sm"
              icon="close"
              color="negative"
              @click="removeElementType(index)"
              title="Remove type"
            />
          </div>

          <!-- Type name and values -->
          <span class="element-type" @click="openValuesSelector(elementType)">
            &lt;{{ formatTypeName(elementType.name) }}&gt;:
          </span>

          <!-- Values display -->
          <span v-if="elementType.selectedValues && elementType.selectedValues.length > 0" class="element-values">
            <draggable
              v-model="elementType.selectedValues"
              item-key="id"
              handle=".value-drag-handle"
              @end="(event) => onValueReorderEnd(event, index)"
              class="values-container"
            >
              <template #item="{ element: value, index: valueIndex }">
                <span
                  class="element-value-container"
                  @mouseenter="hoveredValueIndex = `${index}-${valueIndex}`"
                  @mouseleave="hoveredValueIndex = ''"
                >
                  <!-- Hover controls for value -->
                  <div v-if="hoveredValueIndex === `${index}-${valueIndex}`" class="hover-controls value-controls">
                    <q-btn
                      flat
                      dense
                      size="sm"
                      icon="drag_indicator"
                      class="value-drag-handle"
                      title="Drag to reorder"
                    />
                    <q-btn
                      flat
                      dense
                      size="sm"
                      icon="close"
                      color="negative"
                      @click="removeElementValue(index, valueIndex)"
                      title="Remove value"
                    />
                  </div>

                  <span class="element-value" @click="openValuesSelector(elementType)">
                    {{ value.value }}
                  </span>
                  <span v-if="valueIndex < elementType.selectedValues.length - 1" class="value-separator">, </span>
                </span>
              </template>
            </draggable>
          </span>
          <span v-else class="empty-values">.</span>
        </span>

        <!-- Type separator -->
        <span v-if="index < selectedElementTypes.length - 1" class="type-separator">. </span>
        </template>
      </draggable>

      <!-- Add button -->
      <q-btn
        flat
        dense
        size="sm"
        icon="add"
        class="add-button"
        @click="openTypeSelector"
        label="+"
      />

      <!-- Type selector popup (separate from button) -->
      <q-dialog v-model="showTypeSelector" class="type-selector-dialog">
        <q-card class="type-selector-card">
          <q-card-section class="row items-center q-pb-none">
            <div class="text-h6">Select Element Types</div>
            <q-space />
            <q-btn icon="close" flat round dense v-close-popup />
          </q-card-section>

          <q-card-section>
            <!-- Search input -->
            <q-input
              v-model="typeSearchQuery"
              placeholder="Search element types..."
              dense
              outlined
              class="q-mb-md"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>

            <!-- Types list -->
            <q-list class="types-list" style="max-height: 300px; overflow-y: auto;">
              <q-item
                v-for="elementType in filteredAvailableTypes"
                :key="elementType.id"
                clickable
                @click="toggleTypeSelection(elementType)"
                :class="{ 'selected': tempSelectedTypeIds.includes(elementType.id) }"
              >
                <q-item-section>
                  <q-item-label>{{ formatTypeName(elementType.name) }}</q-item-label>
                  <q-item-label v-if="elementType.description" caption>{{ elementType.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon v-if="tempSelectedTypeIds.includes(elementType.id)" name="check" color="primary" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="Cancel" @click="cancelTypeSelection" />
            <q-btn color="primary" label="Add Selected" @click="confirmTypeSelection" :disable="tempSelectedTypeIds.length === 0" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>

    <!-- Values selector popup -->
    <q-dialog v-model="showValuesSelector" class="values-selector-dialog">
      <q-card class="values-selector-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Select {{ currentTypeForValues ? formatTypeName(currentTypeForValues.name) : '' }} Values</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <!-- Search input -->
          <q-input
            v-model="valueSearchQuery"
            placeholder="Search values..."
            dense
            outlined
            class="q-mb-md"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>

          <!-- Values list -->
          <q-list class="values-list" style="max-height: 300px; overflow-y: auto;">
            <q-item
              v-for="value in filteredAvailableValues"
              :key="value.id"
              clickable
              @click="toggleValueSelection(value)"
              :class="{ 'selected': tempSelectedValueIds.includes(value.id) }"
            >
              <q-item-section>
                <q-item-label>{{ value.value }}</q-item-label>
                <q-item-label v-if="value.description" caption>{{ value.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon v-if="tempSelectedValueIds.includes(value.id)" name="check" color="primary" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" @click="cancelValueSelection" />
          <q-btn color="primary" label="Apply Selection" @click="confirmValueSelection" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { usePromptElementsService } from 'src/services/promptElementsService';
import type { PromptElementType, PromptElement } from 'src/services/promptElementsService';
import draggable from 'vuedraggable';
import { useElementTypeOrderService } from 'src/services/elementTypeOrderService';

// Props
interface Props {
  templateId?: number;
  initialData?: Array<{
    type: PromptElementType;
    values: PromptElement[];
  }>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:data': [data: Array<{ type: PromptElementType; values: PromptElement[] }>];
  'update:prompt': [prompt: string];
}>();

// Services
const promptElementsService = usePromptElementsService();
const elementTypeOrderService = useElementTypeOrderService();

// State
const selectedElementTypes = ref<Array<{
  id: number;
  name: string;
  description?: string | null;
  selectedValues: PromptElement[];
}>>([]);

const availableTypes = ref<PromptElementType[]>([]);
const availableValuesByType = ref<Record<number, PromptElement[]>>({});

// UI State
const showTypeSelector = ref(false);
const showValuesSelector = ref(false);
const typeSearchQuery = ref('');
const valueSearchQuery = ref('');
const tempSelectedTypeIds = ref<number[]>([]);
const tempSelectedValueIds = ref<number[]>([]);
const currentTypeForValues = ref<PromptElementType | null>(null);

// Hover state
const hoveredTypeIndex = ref(-1);
const hoveredValueIndex = ref('');

// Drag and drop state (simplified for vuedraggable)
const isDragging = ref(false);

// Computed
const filteredAvailableTypes = computed(() => {
  const query = typeSearchQuery.value.toLowerCase();
  const alreadySelectedIds = selectedElementTypes.value.map(t => t.id);

  return availableTypes.value
    .filter((type: PromptElementType) => !alreadySelectedIds.includes(type.id))
    .filter((type: PromptElementType) =>
      type.name.toLowerCase().includes(query) ||
      (type.description && type.description.toLowerCase().includes(query))
    );
});

const filteredAvailableValues = computed(() => {
  if (!currentTypeForValues.value) return [];

  const query = valueSearchQuery.value.toLowerCase();
  const values = availableValuesByType.value[currentTypeForValues.value.id] || [];

  return values.filter(value =>
    value.value.toLowerCase().includes(query) ||
    (value.description && value.description.toLowerCase().includes(query))
  );
});

// Methods
function formatTypeName(name: string): string {
  return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

async function loadAvailableTypes() {
  try {
    availableTypes.value = await promptElementsService.getPromptElementTypes();
  } catch (error) {
    console.error('Error loading element types:', error);
  }
}

async function loadValuesForType(typeId: number) {
  if (availableValuesByType.value[typeId]) return;

  try {
    const values = await promptElementsService.getVisiblePromptElements(typeId);
    availableValuesByType.value[typeId] = values;
  } catch (error) {
    console.error(`Error loading values for type ${typeId}:`, error);
  }
}

// Type selection methods
function openTypeSelector() {
  tempSelectedTypeIds.value = [];
  typeSearchQuery.value = '';
  showTypeSelector.value = true;
}

function toggleTypeSelection(elementType: PromptElementType) {
  const index = tempSelectedTypeIds.value.indexOf(elementType.id);
  if (index > -1) {
    tempSelectedTypeIds.value.splice(index, 1);
  } else {
    tempSelectedTypeIds.value.push(elementType.id);
  }
}

function cancelTypeSelection() {
  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
}

async function confirmTypeSelection() {
  for (const typeId of tempSelectedTypeIds.value) {
    const elementType = availableTypes.value.find((t: PromptElementType) => t.id === typeId);
    if (elementType) {
      selectedElementTypes.value.push({
        id: elementType.id,
        name: elementType.name,
        description: elementType.description,
        selectedValues: []
      });

      // Load values for this type
      await loadValuesForType(typeId);
    }
  }

  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
  emitUpdates();
}

// Value selection methods
async function openValuesSelector(elementType: { id: number; name: string; description?: string | null }) {
  const fullType = availableTypes.value.find((t: PromptElementType) => t.id === elementType.id);
  if (!fullType) return;

  currentTypeForValues.value = fullType;

  // Load values if not already loaded
  await loadValuesForType(elementType.id);

  // Set currently selected values
  const selectedType = selectedElementTypes.value.find(t => t.id === elementType.id);
  tempSelectedValueIds.value = selectedType?.selectedValues.map((v: PromptElement) => v.id) || [];

  valueSearchQuery.value = '';
  showValuesSelector.value = true;
}

function toggleValueSelection(value: PromptElement) {
  const index = tempSelectedValueIds.value.indexOf(value.id);
  if (index > -1) {
    tempSelectedValueIds.value.splice(index, 1);
  } else {
    tempSelectedValueIds.value.push(value.id);
  }
}

function cancelValueSelection() {
  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
}

function confirmValueSelection() {
  if (!currentTypeForValues.value) return;

  const selectedType = selectedElementTypes.value.find(t => t.id === currentTypeForValues.value!.id);
  if (!selectedType) return;

  const availableValues = availableValuesByType.value[currentTypeForValues.value.id] || [];
  selectedType.selectedValues = tempSelectedValueIds.value
    .map(id => availableValues.find(v => v.id === id))
    .filter(Boolean) as PromptElement[];

  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
  emitUpdates();
}

// Remove methods
function removeElementType(index: number) {
  selectedElementTypes.value.splice(index, 1);
  emitUpdates();
}

function removeElementValue(typeIndex: number, valueIndex: number) {
  const elementType = selectedElementTypes.value[typeIndex];
  if (elementType) {
    elementType.selectedValues.splice(valueIndex, 1);
    emitUpdates();
  }
}

// Drag & drop handlers for vuedraggable
async function onTypeReorderEnd() {
  console.log('🔄 Type reorder ended');

  // Save the new order to database if we have a template ID
  if (props.templateId) {
    try {
      const orderData = selectedElementTypes.value.map((type, index) => ({
        elementTypeId: type.id,
        order: index
      }));

      console.log('💾 Saving type order:', orderData);

      const result = await elementTypeOrderService.reorderElementTypes(
        props.templateId,
        orderData
      );

      if (result) {
        console.log('✅ Type order saved successfully');
      } else {
        console.error('❌ Failed to save type order');
      }
    } catch (error) {
      console.error('❌ Error saving type order:', error);
    }
  }

  emitUpdates();
}

async function onValueReorderEnd(event: any, typeIndex: number) {
  console.log('🔄 Value reorder ended for type index:', typeIndex);

  const elementType = selectedElementTypes.value[typeIndex];
  if (!elementType || !props.templateId) return;

  try {
    // Save the new value order to database
    const valueOrders = elementType.selectedValues.map((value, index) => ({
      elementId: value.id,
      order: index
    }));

    console.log('💾 Saving value order for type:', elementType.id, valueOrders);

    const result = await promptElementsService.reorderElementValues(
      props.templateId,
      elementType.id,
      valueOrders
    );

    if (result) {
      console.log('✅ Value order saved successfully');
    } else {
      console.error('❌ Failed to save value order');
    }
  } catch (error) {
    console.error('❌ Error saving value order:', error);
  }

  emitUpdates();
}

// Emit updates
function emitUpdates() {
  const data = selectedElementTypes.value.map(type => ({
    type: availableTypes.value.find((t: PromptElementType) => t.id === type.id)!,
    values: type.selectedValues
  }));

  emit('update:data', data);

  // Generate prompt string
  const promptParts = selectedElementTypes.value.map(type => {
    const typeName = formatTypeName(type.name);
    const values = type.selectedValues.map((v: PromptElement) => v.value).join(', ');
    return `<${typeName}>: ${values || '.'}`;
  });

  emit('update:prompt', promptParts.join('. '));
}

// Initialize data from props
async function initializeFromProps() {
  if (props.initialData && props.initialData.length > 0) {
    console.log('🔄 Initializing PromptElementsEditor with data:', props.initialData);

    selectedElementTypes.value = props.initialData.map(item => ({
      id: item.type.id,
      name: item.type.name,
      description: item.type.description,
      selectedValues: item.values
    }));

    // Load values for all types
    for (const item of props.initialData) {
      await loadValuesForType(item.type.id);
    }

    console.log('✅ Initialized selectedElementTypes:', selectedElementTypes.value);
    emitUpdates();
  }
}

// Watch for changes in initialData
watch(() => props.initialData, async (newData) => {
  if (newData && newData.length > 0) {
    console.log('🔄 Props initialData changed, reinitializing:', newData);
    await initializeFromProps();
  }
}, { deep: true, immediate: false });

// Lifecycle
onMounted(async () => {
  await loadAvailableTypes();
  await initializeFromProps();
});
</script>

<style scoped>
.prompt-elements-editor {
  width: 100%;
}

.prompt-display {
  min-height: 40px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: var(--q-dark) ? #1e1e1e : #fafafa;
  color: var(--q-dark) ? #ffffff : #000000;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  position: relative;
}

.element-type-container {
  position: relative;
  display: inline-block;
  margin: 2px;
  transition: all 0.2s ease;
}

.element-type {
  font-weight: 600;
  color: #1976d2;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
}

.element-type:hover {
  background-color: rgba(25, 118, 210, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.element-values {
  margin-left: 4px;
}

.element-value-container {
  position: relative;
  display: inline-block;
  margin: 1px;
  transition: all 0.2s ease;
}

.element-value {
  cursor: pointer;
  padding: 3px 5px;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
}

.element-value:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-values {
  color: #999;
  margin-left: 4px;
}

.value-separator {
  color: #666;
}

.type-separator {
  color: #666;
  margin: 0 4px;
}

.add-button {
  color: #1976d2;
  border: 1px dashed #1976d2;
  border-radius: 50%;
  min-width: 32px;
  height: 32px;
}

.hover-controls {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  display: flex;
  gap: 4px;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.hover-controls .q-btn {
  min-width: 24px;
  height: 24px;
}

.hover-controls .q-btn .q-icon {
  font-size: 14px;
}

.type-drag-handle,
.value-drag-handle {
  cursor: grab;
}

.type-drag-handle:active,
.value-drag-handle:active {
  cursor: grabbing;
}

/* Draggable containers */
.types-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.values-container {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

/* Vuedraggable ghost styles */
.sortable-ghost {
  opacity: 0.5;
  background: rgba(25, 118, 210, 0.1);
  border: 2px dashed #1976d2;
  border-radius: 4px;
}

.type-selector-dialog .q-dialog__inner {
  padding: 16px;
}

.type-selector-card {
  min-width: 400px;
  max-width: 500px;
}

.types-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.values-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.q-item.selected {
  background-color: rgba(25, 118, 210, 0.1);
}

.values-selector-card {
  min-width: 400px;
  max-width: 500px;
}
</style>
