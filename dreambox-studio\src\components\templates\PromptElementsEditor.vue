<template>
  <div class="prompt-elements-editor">
    <!-- Main prompt display area -->
    <div class="prompt-display">
      <!-- Selected types with values -->
      <template v-for="(elementType, index) in selectedElementTypes" :key="elementType.id">
        <!-- Type display -->
        <span
          class="element-type-container"
          :class="{ 'drag-over': dragOverTypeIndex === index }"
          draggable="true"
          @mouseenter="hoveredTypeIndex = index"
          @mouseleave="hoveredTypeIndex = -1"
          @dragstart="startTypeDrag($event, index)"
          @dragover="onTypeDragOver($event, index)"
          @dragleave="onTypeDragLeave"
          @drop="onTypeDrop($event, index)"
          @dragend="onTypeDragEnd"
        >
          <!-- Hover controls for type -->
          <div v-if="hoveredTypeIndex === index" class="hover-controls type-controls">
            <q-btn
              flat
              dense
              size="sm"
              icon="drag_indicator"
              class="drag-handle"
              title="Drag to reorder"
            />
            <q-btn
              flat
              dense
              size="sm"
              icon="close"
              color="negative"
              @click="removeElementType(index)"
              title="Remove type"
            />
          </div>

          <!-- Type name and values -->
          <span class="element-type" @click="openValuesSelector(elementType)">
            &lt;{{ formatTypeName(elementType.name) }}&gt;:
          </span>

          <!-- Values display -->
          <span v-if="elementType.selectedValues && elementType.selectedValues.length > 0" class="element-values">
            <template v-for="(value, valueIndex) in elementType.selectedValues" :key="value.id">
              <span
                class="element-value-container"
                :class="{ 'drag-over': dragOverValueInfo?.typeIndex === index && dragOverValueInfo?.valueIndex === valueIndex }"
                draggable="true"
                @mouseenter="hoveredValueIndex = `${index}-${valueIndex}`"
                @mouseleave="hoveredValueIndex = ''"
                @dragstart="startValueDrag($event, index, valueIndex)"
                @dragover="onValueDragOver($event, index, valueIndex)"
                @dragleave="onValueDragLeave"
                @drop="onValueDrop($event, index, valueIndex)"
                @dragend="onValueDragEnd"
              >
                <!-- Hover controls for value -->
                <div v-if="hoveredValueIndex === `${index}-${valueIndex}`" class="hover-controls value-controls">
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="drag_indicator"
                    class="drag-handle"
                    title="Drag to reorder"
                  />
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="close"
                    color="negative"
                    @click="removeElementValue(index, valueIndex)"
                    title="Remove value"
                  />
                </div>

                <span class="element-value" @click="openValuesSelector(elementType)">
                  {{ value.value }}
                </span>
                <span v-if="valueIndex < elementType.selectedValues.length - 1" class="value-separator">, </span>
              </span>
            </template>
          </span>
          <span v-else class="empty-values">.</span>
        </span>

        <!-- Type separator -->
        <span v-if="index < selectedElementTypes.length - 1" class="type-separator">. </span>
      </template>

      <!-- Add button -->
      <q-btn
        flat
        dense
        size="sm"
        icon="add"
        class="add-button"
        @click="openTypeSelector"
        label="+"
      />

      <!-- Type selector popup (separate from button) -->
      <q-dialog v-model="showTypeSelector" class="type-selector-dialog">
        <q-card class="type-selector-card">
          <q-card-section class="row items-center q-pb-none">
            <div class="text-h6">Select Element Types</div>
            <q-space />
            <q-btn icon="close" flat round dense v-close-popup />
          </q-card-section>

          <q-card-section>
            <!-- Search input -->
            <q-input
              v-model="typeSearchQuery"
              placeholder="Search element types..."
              dense
              outlined
              class="q-mb-md"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>

            <!-- Types list -->
            <q-list class="types-list" style="max-height: 300px; overflow-y: auto;">
              <q-item
                v-for="elementType in filteredAvailableTypes"
                :key="elementType.id"
                clickable
                @click="toggleTypeSelection(elementType)"
                :class="{ 'selected': tempSelectedTypeIds.includes(elementType.id) }"
              >
                <q-item-section>
                  <q-item-label>{{ formatTypeName(elementType.name) }}</q-item-label>
                  <q-item-label v-if="elementType.description" caption>{{ elementType.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon v-if="tempSelectedTypeIds.includes(elementType.id)" name="check" color="primary" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="Cancel" @click="cancelTypeSelection" />
            <q-btn color="primary" label="Add Selected" @click="confirmTypeSelection" :disable="tempSelectedTypeIds.length === 0" />
          </q-card-actions>
        </q-card>
      </q-dialog>
      </q-btn>
    </div>

    <!-- Values selector popup -->
    <q-dialog v-model="showValuesSelector" class="values-selector-dialog">
      <q-card class="values-selector-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Select {{ currentTypeForValues ? formatTypeName(currentTypeForValues.name) : '' }} Values</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <!-- Search input -->
          <q-input
            v-model="valueSearchQuery"
            placeholder="Search values..."
            dense
            outlined
            class="q-mb-md"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>

          <!-- Values list -->
          <q-list class="values-list" style="max-height: 300px; overflow-y: auto;">
            <q-item
              v-for="value in filteredAvailableValues"
              :key="value.id"
              clickable
              @click="toggleValueSelection(value)"
              :class="{ 'selected': tempSelectedValueIds.includes(value.id) }"
            >
              <q-item-section>
                <q-item-label>{{ value.value }}</q-item-label>
                <q-item-label v-if="value.description" caption>{{ value.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon v-if="tempSelectedValueIds.includes(value.id)" name="check" color="primary" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" @click="cancelValueSelection" />
          <q-btn color="primary" label="Apply Selection" @click="confirmValueSelection" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { usePromptElementsService } from 'src/services/promptElementsService';
import type { PromptElementType, PromptElement } from 'src/services/promptElementsService';

// Props
interface Props {
  templateId?: number;
  initialData?: Array<{
    type: PromptElementType;
    values: PromptElement[];
  }>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:data': [data: Array<{ type: PromptElementType; values: PromptElement[] }>];
  'update:prompt': [prompt: string];
}>();

// Services
const promptElementsService = usePromptElementsService();

// State
const selectedElementTypes = ref<Array<{
  id: number;
  name: string;
  description?: string | null;
  selectedValues: PromptElement[];
}>>([]);

const availableTypes = ref<PromptElementType[]>([]);
const availableValuesByType = ref<Record<number, PromptElement[]>>({});

// UI State
const showTypeSelector = ref(false);
const showValuesSelector = ref(false);
const typeSearchQuery = ref('');
const valueSearchQuery = ref('');
const tempSelectedTypeIds = ref<number[]>([]);
const tempSelectedValueIds = ref<number[]>([]);
const currentTypeForValues = ref<PromptElementType | null>(null);

// Hover state
const hoveredTypeIndex = ref(-1);
const hoveredValueIndex = ref('');

// Drag and drop state
const draggedTypeIndex = ref(-1);
const draggedValueInfo = ref<{ typeIndex: number; valueIndex: number } | null>(null);
const dragOverTypeIndex = ref(-1);
const dragOverValueInfo = ref<{ typeIndex: number; valueIndex: number } | null>(null);

// Computed
const filteredAvailableTypes = computed(() => {
  const query = typeSearchQuery.value.toLowerCase();
  const alreadySelectedIds = selectedElementTypes.value.map(t => t.id);

  const filtered = availableTypes.value
    .filter((type: PromptElementType) => !alreadySelectedIds.includes(type.id))
    .filter((type: PromptElementType) =>
      type.name.toLowerCase().includes(query) ||
      (type.description && type.description.toLowerCase().includes(query))
    );

  console.log('🔍 Filtered types:', filtered.length, 'from', availableTypes.value.length, 'total types');
  return filtered;
});

const filteredAvailableValues = computed(() => {
  if (!currentTypeForValues.value) return [];

  const query = valueSearchQuery.value.toLowerCase();
  const values = availableValuesByType.value[currentTypeForValues.value.id] || [];

  return values.filter(value =>
    value.value.toLowerCase().includes(query) ||
    (value.description && value.description.toLowerCase().includes(query))
  );
});

// Methods
function formatTypeName(name: string): string {
  return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

async function loadAvailableTypes() {
  try {
    console.log('🔄 Loading available types...');
    availableTypes.value = await promptElementsService.getPromptElementTypes();
    console.log('✅ Loaded types:', availableTypes.value.length, availableTypes.value);
  } catch (error) {
    console.error('❌ Error loading element types:', error);
  }
}

async function loadValuesForType(typeId: number) {
  if (availableValuesByType.value[typeId]) return;

  try {
    const values = await promptElementsService.getVisiblePromptElements(typeId);
    availableValuesByType.value[typeId] = values;
  } catch (error) {
    console.error(`Error loading values for type ${typeId}:`, error);
  }
}

// Type selection methods
function openTypeSelector() {
  console.log('🔍 Opening type selector...');
  tempSelectedTypeIds.value = [];
  typeSearchQuery.value = '';
  showTypeSelector.value = true;
  console.log('🔍 showTypeSelector set to:', showTypeSelector.value);
}

function toggleTypeSelection(elementType: PromptElementType) {
  const index = tempSelectedTypeIds.value.indexOf(elementType.id);
  if (index > -1) {
    tempSelectedTypeIds.value.splice(index, 1);
  } else {
    tempSelectedTypeIds.value.push(elementType.id);
  }
}

function cancelTypeSelection() {
  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
}

async function confirmTypeSelection() {
  for (const typeId of tempSelectedTypeIds.value) {
    const elementType = availableTypes.value.find((t: PromptElementType) => t.id === typeId);
    if (elementType) {
      selectedElementTypes.value.push({
        id: elementType.id,
        name: elementType.name,
        description: elementType.description,
        selectedValues: []
      });

      // Load values for this type
      await loadValuesForType(typeId);
    }
  }

  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
  emitUpdates();
}

// Value selection methods
async function openValuesSelector(elementType: { id: number; name: string; description?: string | null }) {
  const fullType = availableTypes.value.find((t: PromptElementType) => t.id === elementType.id);
  if (!fullType) return;

  currentTypeForValues.value = fullType;

  // Load values if not already loaded
  await loadValuesForType(elementType.id);

  // Set currently selected values
  const selectedType = selectedElementTypes.value.find(t => t.id === elementType.id);
  tempSelectedValueIds.value = selectedType?.selectedValues.map((v: PromptElement) => v.id) || [];

  valueSearchQuery.value = '';
  showValuesSelector.value = true;
}

function toggleValueSelection(value: PromptElement) {
  const index = tempSelectedValueIds.value.indexOf(value.id);
  if (index > -1) {
    tempSelectedValueIds.value.splice(index, 1);
  } else {
    tempSelectedValueIds.value.push(value.id);
  }
}

function cancelValueSelection() {
  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
}

function confirmValueSelection() {
  if (!currentTypeForValues.value) return;

  const selectedType = selectedElementTypes.value.find(t => t.id === currentTypeForValues.value!.id);
  if (!selectedType) return;

  const availableValues = availableValuesByType.value[currentTypeForValues.value.id] || [];
  selectedType.selectedValues = tempSelectedValueIds.value
    .map(id => availableValues.find(v => v.id === id))
    .filter(Boolean) as PromptElement[];

  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
  emitUpdates();
}

// Remove methods
function removeElementType(index: number) {
  selectedElementTypes.value.splice(index, 1);
  emitUpdates();
}

function removeElementValue(typeIndex: number, valueIndex: number) {
  const elementType = selectedElementTypes.value[typeIndex];
  if (elementType) {
    elementType.selectedValues.splice(valueIndex, 1);
    emitUpdates();
  }
}

// Drag & drop methods for types
function startTypeDrag(event: DragEvent, index: number) {
  if (!event.dataTransfer) return;

  draggedTypeIndex.value = index;
  event.dataTransfer.effectAllowed = 'move';
  event.dataTransfer.setData('text/plain', `type-${index}`);

  // Add visual feedback
  const target = event.target as HTMLElement;
  target.style.opacity = '0.5';
}

function onTypeDragOver(event: DragEvent, index: number) {
  event.preventDefault();
  if (!event.dataTransfer) return;

  event.dataTransfer.dropEffect = 'move';
  dragOverTypeIndex.value = index;
}

function onTypeDragLeave() {
  dragOverTypeIndex.value = -1;
}

function onTypeDrop(event: DragEvent, dropIndex: number) {
  event.preventDefault();

  const dragIndex = draggedTypeIndex.value;
  if (dragIndex === -1 || dragIndex === dropIndex) return;

  // Reorder the types
  const draggedType = selectedElementTypes.value[dragIndex];
  if (!draggedType) return;

  selectedElementTypes.value.splice(dragIndex, 1);
  selectedElementTypes.value.splice(dropIndex, 0, draggedType);

  // Reset drag state
  draggedTypeIndex.value = -1;
  dragOverTypeIndex.value = -1;

  emitUpdates();
}

function onTypeDragEnd(event: DragEvent) {
  // Reset visual feedback
  const target = event.target as HTMLElement;
  target.style.opacity = '1';

  // Reset drag state
  draggedTypeIndex.value = -1;
  dragOverTypeIndex.value = -1;
}

// Drag & drop methods for values
function startValueDrag(event: DragEvent, typeIndex: number, valueIndex: number) {
  if (!event.dataTransfer) return;

  draggedValueInfo.value = { typeIndex, valueIndex };
  event.dataTransfer.effectAllowed = 'move';
  event.dataTransfer.setData('text/plain', `value-${typeIndex}-${valueIndex}`);

  // Add visual feedback
  const target = event.target as HTMLElement;
  target.style.opacity = '0.5';
}

function onValueDragOver(event: DragEvent, typeIndex: number, valueIndex: number) {
  event.preventDefault();
  if (!event.dataTransfer) return;

  // Only allow dropping within the same type
  if (draggedValueInfo.value && draggedValueInfo.value.typeIndex === typeIndex) {
    event.dataTransfer.dropEffect = 'move';
    dragOverValueInfo.value = { typeIndex, valueIndex };
  }
}

function onValueDragLeave() {
  dragOverValueInfo.value = null;
}

function onValueDrop(event: DragEvent, dropTypeIndex: number, dropValueIndex: number) {
  event.preventDefault();

  if (!draggedValueInfo.value) return;

  const { typeIndex: dragTypeIndex, valueIndex: dragValueIndex } = draggedValueInfo.value;

  // Only allow reordering within the same type
  if (dragTypeIndex !== dropTypeIndex) return;
  if (dragValueIndex === dropValueIndex) return;

  // Reorder the values within the type
  const elementType = selectedElementTypes.value[dragTypeIndex];
  if (elementType) {
    const draggedValue = elementType.selectedValues[dragValueIndex];
    if (!draggedValue) return;

    elementType.selectedValues.splice(dragValueIndex, 1);
    elementType.selectedValues.splice(dropValueIndex, 0, draggedValue);
  }

  // Reset drag state
  draggedValueInfo.value = null;
  dragOverValueInfo.value = null;

  emitUpdates();
}

function onValueDragEnd(event: DragEvent) {
  // Reset visual feedback
  const target = event.target as HTMLElement;
  target.style.opacity = '1';

  // Reset drag state
  draggedValueInfo.value = null;
  dragOverValueInfo.value = null;
}

// Emit updates
function emitUpdates() {
  const data = selectedElementTypes.value.map(type => ({
    type: availableTypes.value.find((t: PromptElementType) => t.id === type.id)!,
    values: type.selectedValues
  }));

  emit('update:data', data);

  // Generate prompt string
  const promptParts = selectedElementTypes.value.map(type => {
    const typeName = formatTypeName(type.name);
    const values = type.selectedValues.map((v: PromptElement) => v.value).join(', ');
    return `<${typeName}>: ${values || '.'}`;
  });

  emit('update:prompt', promptParts.join('. '));
}

// Lifecycle
onMounted(async () => {
  console.log('🚀 PromptElementsEditor mounted with props:', props);
  await loadAvailableTypes();

  // Initialize with initial data if provided
  if (props.initialData && props.initialData.length > 0) {
    console.log('📝 Initializing with data:', props.initialData);
    selectedElementTypes.value = props.initialData.map(item => ({
      id: item.type.id,
      name: item.type.name,
      description: item.type.description,
      selectedValues: item.values
    }));

    // Load values for all types
    for (const item of props.initialData) {
      await loadValuesForType(item.type.id);
    }

    emitUpdates();
  } else {
    console.log('📝 No initial data provided');
  }
});
</script>

<style scoped>
.prompt-elements-editor {
  width: 100%;
}

.prompt-display {
  min-height: 40px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: #fafafa;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  position: relative;
}

.element-type-container {
  position: relative;
  display: inline-block;
  margin: 2px;
  transition: all 0.2s ease;
}

.element-type {
  font-weight: 600;
  color: #1976d2;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
}

.element-type:hover {
  background-color: rgba(25, 118, 210, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.element-values {
  margin-left: 4px;
}

.element-value-container {
  position: relative;
  display: inline-block;
  margin: 1px;
  transition: all 0.2s ease;
}

.element-value {
  cursor: pointer;
  padding: 3px 5px;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
}

.element-value:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-values {
  color: #999;
  margin-left: 4px;
}

.value-separator {
  color: #666;
}

.type-separator {
  color: #666;
  margin: 0 4px;
}

.add-button {
  color: #1976d2;
  border: 1px dashed #1976d2;
  border-radius: 50%;
  min-width: 32px;
  height: 32px;
}

.hover-controls {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  display: flex;
  gap: 4px;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.hover-controls .q-btn {
  min-width: 24px;
  height: 24px;
}

.hover-controls .q-btn .q-icon {
  font-size: 14px;
}

.drag-handle {
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Drag and drop visual feedback */
.element-type-container.drag-over {
  background-color: rgba(25, 118, 210, 0.1);
  border: 2px dashed #1976d2;
  border-radius: 4px;
  transform: scale(1.02);
}

.element-value-container.drag-over {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px dashed #4caf50;
  border-radius: 3px;
  transform: scale(1.05);
}

/* Additional drag states are handled by the base classes above */

.type-selector-dialog .q-dialog__inner {
  padding: 16px;
}

.type-selector-card {
  min-width: 400px;
  max-width: 500px;
}

.types-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.values-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.q-item.selected {
  background-color: rgba(25, 118, 210, 0.1);
}

.values-selector-card {
  min-width: 400px;
  max-width: 500px;
}
</style>
