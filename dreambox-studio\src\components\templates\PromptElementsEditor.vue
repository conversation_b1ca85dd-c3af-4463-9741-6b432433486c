<template>
  <div class="prompt-elements-editor">
    <!-- Main prompt display area -->
    <div class="prompt-display">
      <!-- Selected types with values -->
      <template v-for="(elementType, index) in selectedElementTypes" :key="elementType.id">
        <!-- Type display -->
        <span class="element-type-container" @mouseenter="hoveredTypeIndex = index" @mouseleave="hoveredTypeIndex = -1">
          <!-- Hover controls for type -->
          <div v-if="hoveredTypeIndex === index" class="hover-controls type-controls">
            <q-btn
              flat
              dense
              size="sm"
              icon="drag_indicator"
              class="drag-handle"
              @mousedown="startTypeDrag(index)"
            />
            <q-btn
              flat
              dense
              size="sm"
              icon="close"
              @click="removeElementType(index)"
            />
          </div>
          
          <!-- Type name and values -->
          <span class="element-type" @click="openValuesSelector(elementType)">
            &lt;{{ formatTypeName(elementType.name) }}&gt;:
          </span>
          
          <!-- Values display -->
          <span v-if="elementType.selectedValues && elementType.selectedValues.length > 0" class="element-values">
            <template v-for="(value, valueIndex) in elementType.selectedValues" :key="value.id">
              <span 
                class="element-value-container"
                @mouseenter="hoveredValueIndex = `${index}-${valueIndex}`"
                @mouseleave="hoveredValueIndex = ''"
              >
                <!-- Hover controls for value -->
                <div v-if="hoveredValueIndex === `${index}-${valueIndex}`" class="hover-controls value-controls">
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="drag_indicator"
                    class="drag-handle"
                    @mousedown="startValueDrag(index, valueIndex)"
                  />
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="close"
                    @click="removeElementValue(index, valueIndex)"
                  />
                </div>
                
                <span class="element-value" @click="openValuesSelector(elementType)">
                  {{ value.value }}
                </span>
                <span v-if="valueIndex < elementType.selectedValues.length - 1" class="value-separator">, </span>
              </span>
            </template>
          </span>
          <span v-else class="empty-values">.</span>
        </span>
        
        <!-- Type separator -->
        <span v-if="index < selectedElementTypes.length - 1" class="type-separator">. </span>
      </template>
      
      <!-- Add button -->
      <q-btn
        flat
        dense
        size="sm"
        icon="add"
        class="add-button"
        @click="openTypeSelector"
      >
        <q-popup-proxy v-model="showTypeSelector" class="type-selector-popup">
          <div class="type-selector-content">
            <!-- Search input -->
            <q-input
              v-model="typeSearchQuery"
              placeholder="Search element types..."
              dense
              outlined
              class="search-input"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
            
            <!-- Types list -->
            <q-list class="types-list">
              <q-item
                v-for="elementType in filteredAvailableTypes"
                :key="elementType.id"
                clickable
                @click="toggleTypeSelection(elementType)"
                :class="{ 'selected': tempSelectedTypeIds.includes(elementType.id) }"
              >
                <q-item-section>
                  <q-item-label>{{ formatTypeName(elementType.name) }}</q-item-label>
                  <q-item-label v-if="elementType.description" caption>{{ elementType.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon v-if="tempSelectedTypeIds.includes(elementType.id)" name="check" color="primary" />
                </q-item-section>
              </q-item>
            </q-list>
            
            <!-- Action buttons -->
            <div class="selector-actions">
              <q-btn flat label="Cancel" @click="cancelTypeSelection" />
              <q-btn color="primary" label="Add Selected" @click="confirmTypeSelection" :disable="tempSelectedTypeIds.length === 0" />
            </div>
          </div>
        </q-popup-proxy>
      </q-btn>
    </div>
    
    <!-- Values selector popup -->
    <q-dialog v-model="showValuesSelector" class="values-selector-dialog">
      <q-card class="values-selector-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Select {{ currentTypeForValues ? formatTypeName(currentTypeForValues.name) : '' }} Values</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        
        <q-card-section>
          <!-- Search input -->
          <q-input
            v-model="valueSearchQuery"
            placeholder="Search values..."
            dense
            outlined
            class="q-mb-md"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
          
          <!-- Values list -->
          <q-list class="values-list" style="max-height: 300px; overflow-y: auto;">
            <q-item
              v-for="value in filteredAvailableValues"
              :key="value.id"
              clickable
              @click="toggleValueSelection(value)"
              :class="{ 'selected': tempSelectedValueIds.includes(value.id) }"
            >
              <q-item-section>
                <q-item-label>{{ value.value }}</q-item-label>
                <q-item-label v-if="value.description" caption>{{ value.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon v-if="tempSelectedValueIds.includes(value.id)" name="check" color="primary" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat label="Cancel" @click="cancelValueSelection" />
          <q-btn color="primary" label="Apply Selection" @click="confirmValueSelection" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { PromptElementType, PromptElement } from 'src/types/promptElements';
import { usePromptElementsService } from 'src/services/promptElementsService';

// Props
interface Props {
  templateId?: number;
  initialData?: Array<{
    type: PromptElementType;
    values: PromptElement[];
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  templateId: undefined,
  initialData: () => []
});

// Emits
const emit = defineEmits<{
  'update:data': [data: Array<{ type: PromptElementType; values: PromptElement[] }>];
  'update:prompt': [prompt: string];
}>();

// Services
const promptElementsService = usePromptElementsService();

// State
const selectedElementTypes = ref<Array<{
  id: number;
  name: string;
  description?: string;
  selectedValues: PromptElement[];
}>>([]);

const availableTypes = ref<PromptElementType[]>([]);
const availableValuesByType = ref<Record<number, PromptElement[]>>({});

// UI State
const showTypeSelector = ref(false);
const showValuesSelector = ref(false);
const typeSearchQuery = ref('');
const valueSearchQuery = ref('');
const tempSelectedTypeIds = ref<number[]>([]);
const tempSelectedValueIds = ref<number[]>([]);
const currentTypeForValues = ref<PromptElementType | null>(null);

// Hover state
const hoveredTypeIndex = ref(-1);
const hoveredValueIndex = ref('');

// Computed
const filteredAvailableTypes = computed(() => {
  const query = typeSearchQuery.value.toLowerCase();
  const alreadySelectedIds = selectedElementTypes.value.map(t => t.id);
  
  return availableTypes.value
    .filter(type => !alreadySelectedIds.includes(type.id))
    .filter(type => 
      type.name.toLowerCase().includes(query) ||
      (type.description && type.description.toLowerCase().includes(query))
    );
});

const filteredAvailableValues = computed(() => {
  if (!currentTypeForValues.value) return [];
  
  const query = valueSearchQuery.value.toLowerCase();
  const values = availableValuesByType.value[currentTypeForValues.value.id] || [];
  
  return values.filter(value =>
    value.value.toLowerCase().includes(query) ||
    (value.description && value.description.toLowerCase().includes(query))
  );
});

// Methods
function formatTypeName(name: string): string {
  return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

async function loadAvailableTypes() {
  try {
    availableTypes.value = await promptElementsService.getPromptElementTypes();
  } catch (error) {
    console.error('Error loading element types:', error);
  }
}

async function loadValuesForType(typeId: number) {
  if (availableValuesByType.value[typeId]) return;
  
  try {
    const values = await promptElementsService.getVisiblePromptElements(typeId);
    availableValuesByType.value[typeId] = values;
  } catch (error) {
    console.error(`Error loading values for type ${typeId}:`, error);
  }
}

// Type selection methods
function openTypeSelector() {
  tempSelectedTypeIds.value = [];
  typeSearchQuery.value = '';
  showTypeSelector.value = true;
}

function toggleTypeSelection(elementType: PromptElementType) {
  const index = tempSelectedTypeIds.value.indexOf(elementType.id);
  if (index > -1) {
    tempSelectedTypeIds.value.splice(index, 1);
  } else {
    tempSelectedTypeIds.value.push(elementType.id);
  }
}

function cancelTypeSelection() {
  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
}

async function confirmTypeSelection() {
  for (const typeId of tempSelectedTypeIds.value) {
    const elementType = availableTypes.value.find(t => t.id === typeId);
    if (elementType) {
      selectedElementTypes.value.push({
        id: elementType.id,
        name: elementType.name,
        description: elementType.description,
        selectedValues: []
      });
      
      // Load values for this type
      await loadValuesForType(typeId);
    }
  }
  
  showTypeSelector.value = false;
  tempSelectedTypeIds.value = [];
  emitUpdates();
}

// Value selection methods
async function openValuesSelector(elementType: { id: number; name: string; description?: string }) {
  const fullType = availableTypes.value.find(t => t.id === elementType.id);
  if (!fullType) return;
  
  currentTypeForValues.value = fullType;
  
  // Load values if not already loaded
  await loadValuesForType(elementType.id);
  
  // Set currently selected values
  const selectedType = selectedElementTypes.value.find(t => t.id === elementType.id);
  tempSelectedValueIds.value = selectedType?.selectedValues.map(v => v.id) || [];
  
  valueSearchQuery.value = '';
  showValuesSelector.value = true;
}

function toggleValueSelection(value: PromptElement) {
  const index = tempSelectedValueIds.value.indexOf(value.id);
  if (index > -1) {
    tempSelectedValueIds.value.splice(index, 1);
  } else {
    tempSelectedValueIds.value.push(value.id);
  }
}

function cancelValueSelection() {
  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
}

function confirmValueSelection() {
  if (!currentTypeForValues.value) return;
  
  const selectedType = selectedElementTypes.value.find(t => t.id === currentTypeForValues.value!.id);
  if (!selectedType) return;
  
  const availableValues = availableValuesByType.value[currentTypeForValues.value.id] || [];
  selectedType.selectedValues = tempSelectedValueIds.value
    .map(id => availableValues.find(v => v.id === id))
    .filter(Boolean) as PromptElement[];
  
  showValuesSelector.value = false;
  tempSelectedValueIds.value = [];
  currentTypeForValues.value = null;
  emitUpdates();
}

// Remove methods
function removeElementType(index: number) {
  selectedElementTypes.value.splice(index, 1);
  emitUpdates();
}

function removeElementValue(typeIndex: number, valueIndex: number) {
  selectedElementTypes.value[typeIndex].selectedValues.splice(valueIndex, 1);
  emitUpdates();
}

// Drag & drop methods (placeholder for now)
function startTypeDrag(index: number) {
  // TODO: Implement drag & drop
  console.log('Start type drag:', index);
}

function startValueDrag(typeIndex: number, valueIndex: number) {
  // TODO: Implement drag & drop
  console.log('Start value drag:', typeIndex, valueIndex);
}

// Emit updates
function emitUpdates() {
  const data = selectedElementTypes.value.map(type => ({
    type: availableTypes.value.find(t => t.id === type.id)!,
    values: type.selectedValues
  }));
  
  emit('update:data', data);
  
  // Generate prompt string
  const promptParts = selectedElementTypes.value.map(type => {
    const typeName = formatTypeName(type.name);
    const values = type.selectedValues.map(v => v.value).join(', ');
    return `<${typeName}>: ${values || '.'}`;
  });
  
  emit('update:prompt', promptParts.join('. '));
}

// Lifecycle
onMounted(async () => {
  await loadAvailableTypes();
  
  // Initialize with initial data if provided
  if (props.initialData.length > 0) {
    selectedElementTypes.value = props.initialData.map(item => ({
      id: item.type.id,
      name: item.type.name,
      description: item.type.description,
      selectedValues: item.values
    }));
    
    // Load values for all types
    for (const item of props.initialData) {
      await loadValuesForType(item.type.id);
    }
    
    emitUpdates();
  }
});
</script>

<style scoped>
.prompt-elements-editor {
  width: 100%;
}

.prompt-display {
  min-height: 40px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: #fafafa;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  position: relative;
}

.element-type-container {
  position: relative;
  display: inline-block;
}

.element-type {
  font-weight: 600;
  color: #1976d2;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.element-type:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

.element-values {
  margin-left: 4px;
}

.element-value-container {
  position: relative;
  display: inline-block;
}

.element-value {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.element-value:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.empty-values {
  color: #999;
  margin-left: 4px;
}

.value-separator {
  color: #666;
}

.type-separator {
  color: #666;
  margin: 0 4px;
}

.add-button {
  color: #1976d2;
  border: 1px dashed #1976d2;
  border-radius: 50%;
  min-width: 32px;
  height: 32px;
}

.hover-controls {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  display: flex;
  gap: 2px;
}

.type-selector-popup {
  min-width: 300px;
}

.type-selector-content {
  padding: 16px;
}

.search-input {
  margin-bottom: 12px;
}

.types-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.values-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.selector-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.q-item.selected {
  background-color: rgba(25, 118, 210, 0.1);
}

.values-selector-card {
  min-width: 400px;
  max-width: 500px;
}
</style>
