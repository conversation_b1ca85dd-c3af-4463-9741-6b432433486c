import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { comfyUIService } from 'src/services/comfyUI/ComfyUIService';
import { userSettingsService } from 'src/services/userSettingsService';
import type { ServerStatus } from 'src/services/comfyUI/types';

export interface ComfyUIState {
  serverStatus: ServerStatus;
  isCreating: boolean;
  isTerminating: boolean;
  isConnected: boolean;
  selectedGpu: string;
  errorMessage: string;
}

export const useComfyUIStore = defineStore('comfyUI', () => {
  // State
  const serverStatus = ref<ServerStatus>({ status: 'no-server' });
  const isCreating = ref(false);
  const isTerminating = ref(false);
  const isConnected = ref(false);
  const isCheckingHealth = ref(false);
  const selectedGpu = ref('NVIDIA RTX A5000');
  const errorMessage = ref('');
  const terminateOnReload = ref(true); // Default to terminate (safer option)
  const autoTerminationMinutes = ref(30); // Default to 30 minutes
  const lastActivity = ref<Date | null>(null);
  const autoTerminationTimer = ref<number | null>(null);
  const currentTime = ref(new Date()); // For reactive countdown calculation

  // GPU options
  const gpuOptions = [
    { label: 'NVIDIA RTX A5000 (24GB VRAM)', value: 'NVIDIA RTX A5000' },
    { label: 'NVIDIA RTX A4000 (16GB VRAM)', value: 'NVIDIA RTX A4000' },
    { label: 'NVIDIA RTX 3090 (24GB VRAM)', value: 'NVIDIA RTX 3090' },
    { label: 'NVIDIA RTX 4090 (24GB VRAM)', value: 'NVIDIA RTX 4090' }
  ];

  // Computed properties
  const serverExists = computed(() =>
    ['running', 'starting', 'stopping'].includes(serverStatus.value.status)
  );

  const isServerRunning = computed(() => serverStatus.value.status === 'running');

  const statusColor = computed(() => {
    switch (serverStatus.value.status) {
      case 'running':
        return 'positive';
      case 'starting':
        return 'info';
      case 'stopping':
        return 'warning';
      case 'error':
        return 'negative';
      default:
        return 'grey';
    }
  });

  const toggleState = computed(() => {
    if (!serverExists.value) return 'off';

    switch (serverStatus.value.status) {
      case 'starting':
        return 'creating';
      case 'running':
        return isConnected.value ? 'ready' : 'starting-comfyui';
      case 'stopping':
        return 'stopping';
      default:
        return 'off';
    }
  });

  const reloadBehavior = computed(() => {
    // Provide a default value while loading
    return terminateOnReload.value ? 'terminate' : 'keep';
  });

  // Computed properties for auto-termination countdown
  const timeUntilTermination = computed(() => {
    if (!isServerRunning.value || !lastActivity.value || !autoTerminationTimer.value) {
      return null;
    }

    const now = currentTime.value;
    const timeSinceActivity = (now.getTime() - lastActivity.value.getTime()) / (1000 * 60); // minutes
    const timeRemaining = autoTerminationMinutes.value - timeSinceActivity;

    return Math.max(0, timeRemaining); // Don't go below 0
  });

  const terminationProgress = computed(() => {
    if (timeUntilTermination.value === null || !lastActivity.value) return 0;

    // Calculate how much time has actually passed since last activity
    const now = currentTime.value;
    const timeSinceActivity = (now.getTime() - lastActivity.value.getTime()) / (1000 * 60); // minutes

    // Progress represents how much time has passed (0 = just started, 1 = about to terminate)
    const progress = timeSinceActivity / autoTerminationMinutes.value;
    return Math.min(1, Math.max(0, progress)); // Clamp between 0 and 1
  });

  const shouldShowCountdown = computed(() => {
    return isServerRunning.value && autoTerminationTimer.value !== null && lastActivity.value !== null;
  });

  // Actions
  async function updateServerStatus() {
    const status = await comfyUIService.checkServerStatus();
    const wasRunning = serverStatus.value.status === 'running';
    const isNowRunning = status.status === 'running';

    serverStatus.value = status;

    // If server just started running, reset connection state and check readiness
    if (!wasRunning && isNowRunning) {
      isConnected.value = false;
      void waitForComfyUIReadiness();
      startAutoTerminationTimer();
    }
    // If server is running and not connected, check ComfyUI readiness
    else if (isNowRunning && !isConnected.value && !isCheckingHealth.value) {
      void waitForComfyUIReadiness();
      // Start timer if not already running
      if (!autoTerminationTimer.value) {
        startAutoTerminationTimer();
      }
    }
    // If server stopped running, reset connection state and stop timer
    else if (wasRunning && !isNowRunning) {
      isConnected.value = false;
      stopAutoTerminationTimer();
    }

    // IMPORTANT: Don't count automatic status checks as activity
    // Only manual user interactions (like image generation) should reset the timer
  }

  async function startServer(options: { gpuTypeId?: string } = {}) {
    isCreating.value = true;
    errorMessage.value = '';

    try {
      const result = await comfyUIService.startServer({
        gpuTypeId: options.gpuTypeId || selectedGpu.value
      });

      if (result.status === 'error') {
        errorMessage.value = result.message || 'Error creating server';
        return false;
      } else {
        // Update status immediately
        await updateServerStatus();
        // Start auto-termination timer
        startAutoTerminationTimer();
        return true;
      }
    } catch (error) {
      errorMessage.value = error instanceof Error ? error.message : String(error);
      return false;
    } finally {
      isCreating.value = false;
    }
  }

  async function terminateServer() {
    isTerminating.value = true;

    try {
      const success = await comfyUIService.terminateServer();

      if (success) {
        // Stop auto-termination timer
        stopAutoTerminationTimer();
        // Update status immediately
        await updateServerStatus();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error terminating server:', error);
      return false;
    } finally {
      isTerminating.value = false;
    }
  }

  function setConnected(connected: boolean) {
    isConnected.value = connected;
  }

  function setSelectedGpu(gpu: string) {
    selectedGpu.value = gpu;
  }

  function clearError() {
    errorMessage.value = '';
  }

  function updateLastActivity() {
    const now = new Date();
    lastActivity.value = now;
    console.log(`📝 Server activity updated: ${now.toLocaleTimeString()}`);
  }

  // Public function to be called when user interacts with the server
  function recordServerActivity() {
    if (isServerRunning.value) {
      console.log('🎯 Recording server activity (user interaction)');
      const now = new Date();
      lastActivity.value = now;
      currentTime.value = now;
      console.log(`📝 Activity recorded: ${now.toLocaleTimeString()}`);
    } else {
      console.log('⚠️ Cannot record activity: server not running');
    }
  }

  function startAutoTerminationTimer() {
    // Clear existing timer
    if (autoTerminationTimer.value) {
      clearInterval(autoTerminationTimer.value);
      autoTerminationTimer.value = null;
    }

    // Only start timer if server is running
    if (!isServerRunning.value) {
      console.log('Not starting auto-termination timer: server not running');
      return;
    }

    console.log(`🕒 Starting auto-termination timer: ${autoTerminationMinutes.value} minutes`);

    // IMPORTANT: Reset activity to current time and update currentTime
    const now = new Date();
    lastActivity.value = now;
    currentTime.value = now;
    console.log(`📝 Timer started - activity reset to: ${now.toLocaleTimeString()}`);

    // Check every 10 seconds if server should be terminated and update progress
    autoTerminationTimer.value = window.setInterval(() => {
      // Update current time for reactive countdown
      currentTime.value = new Date();

      if (!isServerRunning.value) {
        console.log('⏹️ Server not running, stopping auto-termination timer');
        stopAutoTerminationTimer();
        return;
      }

      if (!lastActivity.value) {
        console.log('⚠️ No last activity recorded');
        return;
      }

      const now = currentTime.value;
      const timeSinceLastActivity = (now.getTime() - lastActivity.value.getTime()) / (1000 * 60); // minutes
      const timeLimit = autoTerminationMinutes.value;

      console.log(`⏰ Auto-termination check: ${timeSinceLastActivity.toFixed(1)}/${timeLimit} minutes since last activity`);

      if (timeSinceLastActivity >= timeLimit) {
        console.log('🔥 AUTO-TERMINATING SERVER DUE TO INACTIVITY');
        stopAutoTerminationTimer(); // Stop timer before terminating
        void terminateServer();
      }
    }, 10000); // Check every 10 seconds for smoother progress updates

    console.log(`✅ Auto-termination timer started with ID: ${autoTerminationTimer.value}`);
  }

  function stopAutoTerminationTimer() {
    if (autoTerminationTimer.value) {
      clearInterval(autoTerminationTimer.value);
      autoTerminationTimer.value = null;
      console.log('Auto-termination timer stopped');
    }
  }

  async function waitForComfyUIReadiness() {
    if (!isServerRunning.value || !serverStatus.value.apiEndpoint) {
      console.log('Server not running or no API endpoint, skipping health check');
      return;
    }

    // If already connected, don't check again
    if (isConnected.value) {
      console.log('ComfyUI already connected, skipping health check');
      return;
    }

    // If already checking, don't start another check
    if (isCheckingHealth.value) {
      console.log('Health check already in progress, skipping');
      return;
    }

    isCheckingHealth.value = true;

    try {
      console.log('Waiting for ComfyUI to be ready...');

      // First, do a quick health check to see if it's already ready
      const quickCheck = await comfyUIService.checkComfyUIHealth(serverStatus.value.apiEndpoint);
      if (quickCheck) {
        isConnected.value = true;
        console.log('ComfyUI is already ready!');
        return;
      }

      // If not ready, wait for it to become ready (up to 5 minutes)
      const isReady = await comfyUIService.waitForComfyUIReady(serverStatus.value.apiEndpoint, 300000);

      if (isReady) {
        isConnected.value = true;
        console.log('✅ ComfyUI is ready!');
        // Don't record activity here - this is automatic, not user interaction
      } else {
        console.log('⏰ ComfyUI readiness check timed out');
      }
    } catch (error) {
      console.error('Error checking ComfyUI readiness:', error);
    } finally {
      isCheckingHealth.value = false;
    }
  }

  async function checkExistingConnection() {
    if (!isServerRunning.value || !serverStatus.value.apiEndpoint) {
      // console.log('Cannot check existing connection: server not running or no endpoint');
      return;
    }

    console.log('🔍 Checking existing ComfyUI connection...');
    // Check if ComfyUI is already ready
    const isHealthy = await comfyUIService.checkComfyUIHealth(serverStatus.value.apiEndpoint);
    if (isHealthy) {
      isConnected.value = true;
      console.log('✅ ComfyUI was already ready');
      // Don't record activity here - this is automatic, not user interaction
    } else {
      console.log('❌ ComfyUI not ready yet');
    }
  }

  async function loadTerminateOnReloadSetting() {
    try {
      const setting = await userSettingsService.getSetting('comfyui-terminate-on-reload');
      if (setting !== null) {
        terminateOnReload.value = setting === 'true';
      }
    } catch (error) {
      console.error('Error loading terminate on reload setting:', error);
    }
  }

  async function loadAutoTerminationSetting() {
    try {
      const setting = await userSettingsService.getSetting('comfyui-auto-termination-minutes');
      if (setting !== null) {
        const minutes = parseInt(setting, 10);
        if (!isNaN(minutes) && minutes >= 5 && minutes <= 60) {
          autoTerminationMinutes.value = minutes;
        }
      }
    } catch (error) {
      console.error('Error loading auto-termination timer setting:', error);
    }
  }

  // Initialize store
  async function initialize() {
    // Load settings first
    await Promise.all([
      loadTerminateOnReloadSetting(),
      loadAutoTerminationSetting()
    ]);
    // Start periodic status checks
    void updateServerStatus();
  }

  return {
    // State
    serverStatus,
    isCreating,
    isTerminating,
    isConnected,
    isCheckingHealth,
    selectedGpu,
    errorMessage,
    autoTerminationMinutes,
    lastActivity,
    autoTerminationTimer,
    currentTime,
    gpuOptions,

    // Computed
    serverExists,
    isServerRunning,
    statusColor,
    toggleState,
    reloadBehavior,
    timeUntilTermination,
    terminationProgress,
    shouldShowCountdown,

    // Actions
    updateServerStatus,
    startServer,
    terminateServer,
    setConnected,
    setSelectedGpu,
    clearError,
    waitForComfyUIReadiness,
    checkExistingConnection,
    updateLastActivity,
    recordServerActivity,
    startAutoTerminationTimer,
    stopAutoTerminationTimer,
    initialize
  };
});
