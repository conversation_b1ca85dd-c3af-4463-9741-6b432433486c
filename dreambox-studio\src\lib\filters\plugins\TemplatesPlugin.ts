/**
 * TemplatesPlugin.ts
 *
 * This plugin provides template-specific filter functionality for the filter engine.
 * It registers template-specific filter types and provides template-specific filtering logic.
 */

import type { FilterEngine, FilterPlugin } from '../FilterEngine';
import type { FilterTypeDefinition } from '../FilterRegistry';
import { filterRegistry } from '../FilterRegistry';
import PromptElementsFilter from '../specialized/PromptElementsFilter.vue';
import RelatedItemsFilter from '../specialized/RelatedItemsFilter.vue';
import FilterControl from '../components/FilterControl.vue';

/**
 * Templates Plugin class
 */
export class TemplatesPlugin implements FilterPlugin {
  id = 'templates';
  name = 'Templates Plugin';

  private engine: FilterEngine | null = null;

  /**
   * Initialize the plugin
   *
   * @param engine Filter engine instance
   */
  initialize(engine: FilterEngine): void {
    this.engine = engine;

    // Register template-specific filter types
    this.registerFilterTypes();

    // console.log('Templates Plugin initialized');
  }

  /**
   * Apply template-specific filters
   *
   * @param data Data to filter
   * @param tableName Table name
   * @param filters Filter values
   * @returns Filtered data
   */
  applyFilters<T>(
    data: T[],
    tableName: string,
    filters: Record<string, unknown>
  ): T[] {
    // Only apply filters for templates table
    if (tableName !== 'templates') return data;

    // Use the engine's standard filter application
    if (!this.engine) return data;

    // Extract standard and specialized filters
    const standardFilters: Record<string, unknown> = {};
    const specializedFilters: Record<string, unknown> = {};

    // Separate standard and specialized filters
    for (const [key, value] of Object.entries(filters)) {
      if (key === 'promptElements' || key === 'relatedItems') {
        specializedFilters[key] = value;
      } else {
        standardFilters[key] = value;
      }
    }

    // Apply filters using the engine
    return this.engine.applyFilters(
      data,
      tableName,
      standardFilters,
      specializedFilters
    );
  }

  /**
   * Get plugin-specific components
   *
   * @returns Component map
   */
  getComponents(): Record<string, unknown> {
    return {
      PromptElementsFilter,
      RelatedItemsFilter
    };
  }

  /**
   * Get plugin-specific filter types
   *
   * @returns Filter type definitions
   */
  getFilterTypes(): FilterTypeDefinition[] {
    return this.getTemplateFilterTypes();
  }

  /**
   * Register template-specific filter types
   */
  private registerFilterTypes(): void {
    // Register all template filter types
    for (const filterType of this.getTemplateFilterTypes()) {
      filterRegistry.registerFilterType(filterType);
    }
  }

  /**
   * Get template filter types
   *
   * @returns Template filter type definitions
   */
  private getTemplateFilterTypes(): FilterTypeDefinition[] {
    return [
      // Standard column filters
      {
        id: 'name',
        name: 'Name',
        section: 'main',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          const template = item as Record<string, unknown>;
          const nameValue = template.name;
          // Handle null or undefined name
          const name = typeof nameValue === 'string' ? nameValue.toLowerCase() : '';
          // Ensure value is a string
          let valueStr = '';
          if (typeof value === 'string') {
            valueStr = value.toLowerCase();
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            valueStr = String(value).toLowerCase();
          } else if (value) {
            // For other types, use a safe default
            valueStr = '';
          }
          return name.includes(valueStr);
        }
      },
      {
        id: 'description',
        name: 'Description',
        section: 'expanded',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          const template = item as Record<string, unknown>;
          const descValue = template.description;
          // Handle null or undefined description
          const description = typeof descValue === 'string' ? descValue.toLowerCase() : '';
          // Ensure value is a string
          let valueStr = '';
          if (typeof value === 'string') {
            valueStr = value.toLowerCase();
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            valueStr = String(value).toLowerCase();
          } else if (value) {
            // For other types, use a safe default
            valueStr = '';
          }
          return description.includes(valueStr);
        }
      },
      {
        id: 'status',
        name: 'Status',
        section: 'main',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          const template = item as Record<string, unknown>;
          return template.status === value;
        }
      },
      {
        id: 'created_at',
        name: 'Created Date',
        section: 'expanded',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          // Date filtering logic would go here
          return true;
        }
      },
      {
        id: 'updated_at',
        name: 'Updated Date',
        section: 'expanded',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          // Date filtering logic would go here
          return true;
        }
      },
      {
        id: 'designer_id',
        name: 'Designer',
        section: 'expanded',
        component: FilterControl,
        tableTypes: ['templates'],
        stateExtractor: (state) => state,
        stateApplier: (item, value) => {
          if (!value) return true;
          const template = item as Record<string, unknown>;
          return template.designer_id === value;
        }
      },

      // Specialized filters
      {
        id: 'promptElements',
        name: 'Prompt Elements',
        section: 'specialized',
        component: PromptElementsFilter,
        tableTypes: ['templates'],
        stateExtractor: (state) => {
          if (state && typeof state === 'object' && state !== null && 'promptElements' in state) {
            return (state as Record<string, unknown>).promptElements;
          }
          return null;
        },
        stateApplier: (item, value) => {
          if (!value || typeof value !== 'object' || value === null) return true;

          const filterValue = value as {
            elements?: number[],
            matchType?: 'all' | 'any'
          };

          if (!filterValue.elements || !Array.isArray(filterValue.elements) || filterValue.elements.length === 0) {
            return true;
          }

          const template = item as Record<string, unknown>;
          if (!template.prompt_elements || !Array.isArray(template.prompt_elements)) {
            return false;
          }

          // Get element IDs from the template
          const templateElementIds = (template.prompt_elements as Array<{ id: number }>).map(el => el.id);

          // Check if the template contains the required elements
          if (filterValue.matchType === 'all') {
            // Must contain all selected elements
            return filterValue.elements.every(id => templateElementIds.includes(id));
          } else {
            // Must contain at least one selected element
            return filterValue.elements.some(id => templateElementIds.includes(id));
          }
        }
      },
      {
        id: 'relatedItems',
        name: 'Related Items',
        section: 'specialized',
        component: RelatedItemsFilter,
        tableTypes: ['templates'],
        stateExtractor: (state) => {
          if (state && typeof state === 'object' && state !== null && 'relatedItems' in state) {
            return (state as Record<string, unknown>).relatedItems;
          }
          return null;
        },
        stateApplier: (item, value) => {
          if (!value || typeof value !== 'object' || value === null) return true;

          const filterValue = value as {
            items?: number[],
            matchType?: 'all' | 'any',
            type?: 'collections' | 'products'
          };

          if (!filterValue.items || !Array.isArray(filterValue.items) || filterValue.items.length === 0) {
            return true;
          }

          const template = item as Record<string, unknown>;

          // Check based on relation type
          if (filterValue.type === 'collections') {
            // Check if the template is in the selected collections
            if (!template.collections || !Array.isArray(template.collections)) {
              return false;
            }

            const templateCollectionIds = (template.collections as Array<{ id: number }>).map(col => col.id);

            if (filterValue.matchType === 'all') {
              // Must be in all selected collections
              return filterValue.items.every(id => templateCollectionIds.includes(id));
            } else {
              // Must be in at least one selected collection
              return filterValue.items.some(id => templateCollectionIds.includes(id));
            }
          } else if (filterValue.type === 'products') {
            // Check if the template is associated with the selected products
            if (!template.products || !Array.isArray(template.products)) {
              return false;
            }

            const templateProductIds = (template.products as Array<{ id: number }>).map(prod => prod.id);

            if (filterValue.matchType === 'all') {
              // Must be associated with all selected products
              return filterValue.items.every(id => templateProductIds.includes(id));
            } else {
              // Must be associated with at least one selected product
              return filterValue.items.some(id => templateProductIds.includes(id));
            }
          }

          return true;
        }
      }
    ];
  }
}

// Export default for module imports
export default TemplatesPlugin;
