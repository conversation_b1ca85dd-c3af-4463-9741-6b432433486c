First, pay augment subscription... free account is not enough anymore...

Ok, view action is not needed for Draft templates, only edit.

Add Generate image action to more menu on templates.
When template is selected, we should display all images for this template in the right drawer > preview tab.

Templates: add custom values to element types !!!
Add Story field - dropdown with list of stories and possiblity to add new story

Prompt elements editor ?

Connect events with templates !!!
Related data: Collections, Products, Events

Event should be incorporated into story somehow...

Prompt builder: generate prompt from prompt elements (text).

S3 image organizer (delete old/unused images)

get json data from comfyUI image? prompt, negative prompt, seed, steps, cfg scale, etc.

prompt elements: Selected types: 4 | Types shown: 4 + reload button -> this can go out...

Prompt text should be generated from prompt elements automatically, as we choose them...
I envision something like editable text, where every word would be element, which can be selected and dropdown would appear with list of available values... on the end of text there would be + button, which would add new element (selected from dropdown)
This should be a custom Quasar component.

Jer nikako ne mogu prožvakat taj UI, nekako mi ne ide, ne izgleda mi pregledno, produktivno, intuitivno...

Something like:

<Story>: apocalyptic futuristic industrial landscape, <Mood/Atmosphere>: Industrial, Mysterius, <Composition>: Diagonal Flow, <Camera-Settings>: f/16 aperture, <BlendConcepts>: Dream-Reality Mix, <+> 

Ako bi se moglo i pisat u njega, to bi bilo savršeno, čini mi se...
Ali trebalo bi omogučit i drag/drop i delete - te kontrole bi se pojavile samo na hover


Image to prompt tool. This would be the top thing, maximum what we can achieve (https://imageprompt.org/image-to-prompt), jako dobar i besplatan...

Ovaj...nije tako dobar, ali ima api:
(https://novita.ai/docs/api-reference/model-apis-image-to-prompt)
api key (for testing): sk_XHqnqlpb5pCsQkFBVC5YLZuGFU8-UfAw5Caiqj7uICw
login: <EMAIL>


Ok, I would like to completely rehaul Prompt Elements section in Tempates table. 
Longer I have this UI, more I feel that it is not really clear and intuitive.
One way is to leave it as it is (multiple dropdowns etc.) and to build prompt text from all selected prompt elements.
Second option is to build custom Quasar component for this. This component would look like text, but each word would be element, which can be selected and dropdown would appear with list of available values... on the end of text there would be + button, which would add new element (selected from dropdown)

Something like:

<Story>: apocalyptic futuristic industrial landscape, <Mood/Atmosphere>: Industrial, Mysterius, <Composition>: Diagonal Flow, <Camera-Settings>: f/16 aperture, <BlendConcepts>: Dream-Reality Mix, <+> 

Story, Mood/Atmosphere, Composition, Camera-Settings, BlendConcepts are prompt element types and when clicked, dropdown would appear with list of available values.
+ button on the end would add new element type

There would be possibility to write additional elements and additional types and values (which are not in dropdowns and these values would be inserted in supabase) and it would be possible to delete/reorder them (like it is already possible now). UI for delete/drag&drop would appear on hover.

Do you understand what I want to achieve? Please give me your thoughts... No code, just discussion.

Ok, you encouraged me, so let's build this component. On beginning there will be only + button,
when we click on this button, popup with list of available prompt element types will appear.
This list should have search on top and below that list of available types. Dropdown should enable
multiselect and color selected items in blue (like we have now).
When types are selected (for example, Story, Mood/Atmosphere, Composition, Camera-Settings, BlendConcepts), text in component should be changed is this:

<Story>: . <Mood/Atmosphere>: . <Composition>: . <Camera-Settings>: . <BlendConcepts>: . <+> 

Every type when clicked, dropdown with values for that type should appear. Dropdown should enable
multiselect and color selected items in blue (like we have now).
After values are selected, text should be changed to this:

<Story>: apocalyptic futuristic industrial landscape. <Mood/Atmosphere>: Industrial, Mysterius. <Composition>: Diagonal Flow. <Camera-Settings>: f/16 aperture. <BlendConcepts>: Dream-Reality Mix. <+> 

When type is hovered, delete icon and drag/drop icon should appear above the type.
When value is hovered, delete icon and drag/drop icon should appear above the value.
Types can be reordered so when we reorder them, text would look like this:

<Composition>: Diagonal Flow. <Camera-Settings>: f/16 aperture. <BlendConcepts>: Dream-Reality Mix. <Story>: apocalyptic futuristic industrial landscape. <Mood/Atmosphere>: Industrial, Mysterius. <+> 

Values for specific type can be reordered as well, so when we reorder values for Mood/Atmosphere, text would look like this:

<Story>: apocalyptic futuristic industrial landscape. <Mood/Atmosphere>: Mysterius, Industrial.  <Composition>: Diagonal Flow. <Camera-Settings>: f/16 aperture. <BlendConcepts>: Dream-Reality Mix. <+> 

Types separator is dot, values separator is comma.

We should put this component under the Prompt Elements section in Elements tab in templates table.
When we will be satisfied with result, we will remove existing prompt elements section and replace it with this component.

I added story to prompt_element_types table (id 31). I also added two stories to prompt_elements table (id 543 and 544).
Currently I can add story type in current UI, but values are not appearing in story values dropdown, I don't know why, we need to investigate this. Values dropdown shows No matching values. 
In filter engine (left drawer), there is no problem with story values, they are appearing in dropdown.

Do you have any questions or it is clear enough?