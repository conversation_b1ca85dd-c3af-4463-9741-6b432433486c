<template>
  <div class="template-table">
    <!-- Show skeleton loader when loading -->
    <div v-if="loading" class="skeleton-container">
      <q-skeleton type="rect" class="q-mb-md" height="50px" />
      <q-skeleton type="rect" class="q-mb-md" height="50px" />
      <q-skeleton type="rect" class="q-mb-md" height="50px" />
      <q-skeleton type="rect" class="q-mb-md" height="50px" />
      <q-skeleton type="rect" class="q-mb-md" height="50px" />
    </div>

    <!-- Show actual table when not loading -->
    <q-table
      v-else
      :rows="templates"
      :columns="tableColumns"
      :loading="loading"
      v-model:pagination="tablePagination"
      v-model:selected="selectedRows"
      v-model:expanded="expandedRows"
      selection="multiple"
      row-key="id"
      binary-state-sort
      @request="onRequest"
      :rows-per-page-options="[10, 20, 50, 100]"
      class="no-default-spinner"
    >
      <!-- Custom header template to add empty cell for expand button column -->
      <template v-slot:header="props">
        <q-tr :props="props">
          <!-- Selection checkbox header cell -->
          <q-th class="checkbox-column" style="width: 40px">
            <div class="row justify-center">
              <q-checkbox v-model="props.selected" />
            </div>
          </q-th>

          <!-- Expand button column header (empty) -->
          <q-th class="expand-column" style="width: 40px">
            <div class="row justify-center">
              <!-- Empty header for expand button column -->
            </div>
          </q-th>

          <!-- Regular column headers -->
          <q-th
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
            :class="col.classes"
          >
            <!-- Special handling for actions column header -->
            <template v-if="col.name === 'actions'">
              <div class="row justify-center">
                <q-btn
                  v-if="selectedRows.length > 1"
                  flat
                  round
                  dense
                  size="sm"
                  color="grey-7"
                  icon="more_vert"
                >
                  <q-menu anchor="bottom right" self="top right">
                    <q-list style="min-width: 200px">
                      <q-item-label header>{{ t('templates.batchActions') }}</q-item-label>

                      <!-- Submit for Approval (Draft -> Pending) -->
                      <q-item
                        v-if="canBatchSubmitForApproval"
                        clickable
                        v-close-popup
                        @click="batchUpdateStatus('pending')"
                      >
                        <q-item-section avatar>
                          <q-icon color="orange" name="send" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.submitForApproval') }}</q-item-section>
                      </q-item>

                      <!-- Approve (Draft/Pending -> Active) -->
                      <q-item
                        v-if="canBatchApprove"
                        clickable
                        v-close-popup
                        @click="batchUpdateStatus('active')"
                      >
                        <q-item-section avatar>
                          <q-icon color="positive" name="check_circle" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.approve') }}</q-item-section>
                      </q-item>

                      <!-- Reject (Pending -> Draft) -->
                      <q-item
                        v-if="canBatchReject"
                        clickable
                        v-close-popup
                        @click="batchUpdateStatus('draft')"
                      >
                        <q-item-section avatar>
                          <q-icon color="negative" name="cancel" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.reject') }}</q-item-section>
                      </q-item>

                      <!-- Archive (Active -> Archived) -->
                      <q-item
                        v-if="canBatchArchive"
                        clickable
                        v-close-popup
                        @click="confirmBatchArchive"
                      >
                        <q-item-section avatar>
                          <q-icon color="grey" name="archive" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.archive') }}</q-item-section>
                      </q-item>

                      <!-- Copy to New Draft (Any status) -->
                      <q-item
                        v-if="selectedTemplatesHaveSameStatus"
                        clickable
                        v-close-popup
                        @click="batchCopyTemplates"
                      >
                        <q-item-section avatar>
                          <q-icon color="blue" name="content_copy" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.copyToNewDraft') }}</q-item-section>
                      </q-item>

                      <q-separator />

                      <!-- Clear Selection -->
                      <q-item
                        clickable
                        v-close-popup
                        @click="clearSelection"
                      >
                        <q-item-section avatar>
                          <q-icon color="primary" name="clear" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.clearSelection') }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </div>
            </template>
            <template v-else>
              {{ col.label }}
            </template>
          </q-th>
        </q-tr>
      </template>

      <!-- Custom body template for rows and expanded content -->
      <template v-slot:body="props">
        <q-tr :props="props" :class="{ 'selected-row': props.selected }" :data-id="props.row.id">
          <!-- Selection checkbox column -->
          <q-td class="checkbox-column" style="width: 40px">
            <div class="row justify-center">
              <q-checkbox
                v-model="props.selected"
                @click.stop
                @update:model-value="handleCheckboxChange($event, props.rowIndex)"
                @mousedown="handleCheckboxMouseDown($event, props.rowIndex)"
                @mouseover="handleCheckboxMouseOver(props.rowIndex)"
                :class="{ 'drag-selectable': altKeyPressed }"
              />
            </div>
          </q-td>

          <!-- Expand button column -->
          <q-td class="expand-column" style="width: 40px">
            <div class="row justify-center">
              <q-btn
                size="sm"
                color="accent"
                round
                dense
                :icon="props.expand ? 'remove' : 'add'"
                @click.stop="props.expand = !props.expand"
              />
            </div>
          </q-td>

          <!-- Regular columns -->
          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            <!-- Status column with chip -->
            <template v-if="col.name === 'status'">
              <q-badge
                :color="getStatusColor(props.row.status)"
                class="status-badge"
              >
                {{ props.row.status }}
              </q-badge>
            </template>

            <!-- Prompt elements column with chips -->
            <template v-else-if="col.name === 'prompt_elements'">
              <div v-if="props.row.prompt_elements && props.row.prompt_elements.length > 0">
                <q-chip
                  v-for="element in props.row.prompt_elements.slice(0, 3)"
                  :key="element.id"
                  color="secondary"
                  text-color="white"
                  dense
                  class="q-mr-xs"
                >
                  {{ element.name }}
                </q-chip>
                <q-chip
                  v-if="props.row.prompt_elements.length > 3"
                  color="grey"
                  text-color="white"
                  dense
                >
                  +{{ props.row.prompt_elements.length - 3 }} more
                </q-chip>
              </div>
              <div v-else class="text-grey">{{ t('templates.noElements') }}</div>
            </template>

            <!-- Collections column with chips -->
            <template v-else-if="col.name === 'collections'">
              <div v-if="props.row.collections && props.row.collections.length > 0">
                <q-chip
                  v-for="collection in props.row.collections.slice(0, 3)"
                  :key="collection.id"
                  color="primary"
                  text-color="white"
                  dense
                  class="q-mr-xs"
                >
                  {{ collection.name }}
                </q-chip>
                <q-chip
                  v-if="props.row.collections.length > 3"
                  color="grey"
                  text-color="white"
                  dense
                >
                  +{{ props.row.collections.length - 3 }} more
                </q-chip>
              </div>
              <div v-else class="text-grey">No collections</div>
            </template>

            <!-- Products column with chips -->
            <template v-else-if="col.name === 'products'">
              <div v-if="props.row.products && props.row.products.length > 0">
                <q-chip
                  v-for="product in props.row.products.slice(0, 3)"
                  :key="product.id"
                  color="accent"
                  text-color="white"
                  dense
                  class="q-mr-xs"
                >
                  {{ product.name }}
                </q-chip>
                <q-chip
                  v-if="props.row.products.length > 3"
                  color="grey"
                  text-color="white"
                  dense
                >
                  +{{ props.row.products.length - 3 }} more
                </q-chip>
              </div>
              <div v-else class="text-grey">{{ t('templates.noProducts') }}</div>
            </template>

            <!-- Actions column -->
            <template v-else-if="col.name === 'actions'">
              <div class="row justify-center">
                <q-btn
                  flat
                  round
                  dense
                  size="sm"
                  color="grey-7"
                  icon="more_vert"
                  @click.stop
                >
                  <q-menu anchor="bottom right" self="top right">
                    <q-list style="min-width: 150px">
                      <!-- Edit action - only for draft and pending templates -->
                      <q-item
                        v-if="['draft', 'pending'].includes(props.row.status)"
                        clickable
                        v-close-popup
                        @click="$emit('view', props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon color="primary" name="edit" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.edit') }}</q-item-section>
                      </q-item>

                      <!-- View action -->
                      <q-item clickable v-close-popup @click="$emit('view', props.row)">
                        <q-item-section avatar>
                          <q-icon color="secondary" name="visibility" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.view') }}</q-item-section>
                      </q-item>

                      <q-separator />

                      <!-- Status actions based on lifecycle -->
                      <!-- Designer: Draft -> Pending -->
                      <q-item
                        v-if="props.row.status === 'draft' && hasRole('designer')"
                        clickable
                        v-close-popup
                        @click="updateTemplateStatus(props.row, 'pending')"
                      >
                        <q-item-section avatar>
                          <q-icon color="orange" name="send" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.submitForApproval') }}</q-item-section>
                      </q-item>

                      <!-- Admin: Draft -> Active (direct approval) -->
                      <q-item
                        v-if="props.row.status === 'draft' && hasRole('admin')"
                        clickable
                        v-close-popup
                        @click="updateTemplateStatus(props.row, 'active')"
                      >
                        <q-item-section avatar>
                          <q-icon color="positive" name="check_circle" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.approve') }}</q-item-section>
                      </q-item>

                      <!-- Admin: Pending -> Active (approval) -->
                      <q-item
                        v-if="props.row.status === 'pending' && hasRole('admin')"
                        clickable
                        v-close-popup
                        @click="updateTemplateStatus(props.row, 'active')"
                      >
                        <q-item-section avatar>
                          <q-icon color="positive" name="check_circle" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.approve') }}</q-item-section>
                      </q-item>

                      <!-- Admin: Pending -> Draft (rejection) -->
                      <q-item
                        v-if="props.row.status === 'pending' && hasRole('admin')"
                        clickable
                        v-close-popup
                        @click="updateTemplateStatus(props.row, 'draft')"
                      >
                        <q-item-section avatar>
                          <q-icon color="negative" name="cancel" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.reject') }}</q-item-section>
                      </q-item>

                      <!-- Admin: Active -> Archived -->
                      <q-item
                        v-if="props.row.status === 'active' && hasRole('admin')"
                        clickable
                        v-close-popup
                        @click="updateTemplateStatus(props.row, 'archived')"
                      >
                        <q-item-section avatar>
                          <q-icon color="grey" name="archive" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.archive') }}</q-item-section>
                      </q-item>

                      <q-separator />

                      <!-- Copy to New Draft for all statuses -->
                      <q-item
                        clickable
                        v-close-popup
                        @click="copyTemplate(props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon color="blue" name="content_copy" />
                        </q-item-section>
                        <q-item-section>{{ t('templates.copyToNewDraft') }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </div>
            </template>

            <!-- Default column rendering -->
            <template v-else>
              {{ props.row[col.field] }}
            </template>
          </q-td>
        </q-tr>

        <!-- Expanded row content - separate row -->
        <q-tr v-show="props.expand" :props="props" class="expanded-row">
          <!-- Use the same number of columns as the main row -->
          <q-td :colspan="tableColumns.length + 2">
            <div class="q-pa-md expanded-content">
              <!-- Tabbed layout for all screen sizes -->
              <div class="q-mt-md">
                <!-- Tab navigation -->
                <q-tabs
                  v-model="expandedTab"
                  dense
                  class="text-primary responsive-tabs"
                  active-color="primary"
                  indicator-color="primary"
                  align="justify"
                  narrow-indicator
                  @update:model-value="(tab) => onTabChange(tab, props.row.id)"
                >
                  <q-tab name="elements" icon="category" :label="t('templates.elements')" class="desktop-label">
                    <q-tooltip class="mobile-only">{{ t('templates.elements') }}</q-tooltip>
                  </q-tab>
                  <q-tab name="related" icon="link" :label="t('templates.related')" class="desktop-label">
                    <q-tooltip class="mobile-only">{{ t('templates.related') }}</q-tooltip>
                  </q-tab>
                  <q-tab name="details" icon="info" :label="t('templates.details')" class="desktop-label">
                    <q-tooltip class="mobile-only">{{ t('templates.details') }}</q-tooltip>
                  </q-tab>
                  <q-tab name="history" icon="history" :label="t('templates.statusHistory')" class="desktop-label">
                    <q-tooltip class="mobile-only">{{ t('templates.statusHistory') }}</q-tooltip>
                  </q-tab>
                </q-tabs>

                <!-- Tab content - using v-show instead of v-if to preserve component state -->
                <div class="q-mt-md">
                  <!-- Elements Tab Content -->
                  <div v-show="expandedTab === 'elements'" class="tab-content">
                    <div class="text-h6 q-mb-md">{{ t('templates.promptElements') }}</div>
                    <!-- No need for keep-alive since we're using v-show -->
                    <prompt-element-selector
                      :key="`elements-${props.row.id}`"
                      :template-id="props.row.id"
                      @update="(elements) => onElementsUpdate(elements, props.row.id)"
                    />

                    <!-- New Prompt Elements Editor (Testing) -->
                    <div class="q-mt-lg">
                      <div class="text-h6 q-mb-md">New Prompt Elements Editor (Preview)</div>
                      <prompt-elements-editor
                        :key="`new-elements-${props.row.id}`"
                        :template-id="props.row.id"
                        :initial-data="templateElementsData[props.row.id] || []"
                        @update:data="(data) => onNewElementsUpdate(data, props.row.id)"
                        @update:prompt="(prompt) => onPromptUpdate(prompt, props.row.id)"
                      />
                    </div>
                  </div>

                  <!-- Related Items Tab Content -->
                  <div v-show="expandedTab === 'related'" class="tab-content">
                    <div class="text-h6 q-mb-md">{{ t('filters.relatedItems') }}</div>
                    <div class="row q-col-gutter-md">
                      <!-- Collections -->
                      <div class="col-12 col-md-6">
                        <q-card flat bordered>
                          <q-card-section>
                            <div class="text-subtitle1">{{ t('templates.collections') }}</div>
                          </q-card-section>
                          <q-separator />
                          <q-card-section>
                            <div v-if="props.row.collections && props.row.collections.length > 0">
                              <q-list>
                                <q-item v-for="collection in props.row.collections" :key="collection.id">
                                  <q-item-section>
                                    <q-item-label>{{ collection.name }}</q-item-label>
                                  </q-item-section>
                                </q-item>
                              </q-list>
                            </div>
                            <div v-else class="text-grey">{{ t('templates.noCollections') }}</div>
                          </q-card-section>
                        </q-card>
                      </div>

                      <!-- Products -->
                      <div class="col-12 col-md-6">
                        <q-card flat bordered>
                          <q-card-section>
                            <div class="text-subtitle1">{{ t('templates.products') }}</div>
                          </q-card-section>
                          <q-separator />
                          <q-card-section>
                            <div v-if="props.row.products && props.row.products.length > 0">
                              <q-list>
                                <q-item v-for="product in props.row.products" :key="product.id">
                                  <q-item-section>
                                    <q-item-label>{{ product.name }}</q-item-label>
                                  </q-item-section>
                                </q-item>
                              </q-list>
                            </div>
                            <div v-else class="text-grey">No products associated with this template.</div>
                          </q-card-section>
                        </q-card>
                      </div>
                    </div>
                  </div>

                  <!-- Details Tab Content -->
                  <div v-show="expandedTab === 'details'" class="tab-content">
                    <div class="text-h6 q-mb-md">Template Details</div>
                    <!-- Responsive grid layout for details - each field is its own card -->
                    <div class="details-grid">
                      <!-- Generate a card for each field in expandedColumns -->
                      <template v-for="field in detailFields" :key="field.name">
                        <q-card v-if="props.row[field.field] !== undefined && props.row[field.field] !== null"
                                flat bordered class="field-card">
                          <q-card-section class="q-py-xs">
                            <div class="text-caption text-weight-medium">{{ field.label }}</div>
                          </q-card-section>
                          <q-card-section class="q-pt-none">
                            <!-- Format dates -->
                            <div v-if="field.field.includes('date') || field.field.includes('_at')" class="field-value">
                              {{ formatDate(props.row[field.field]) }}
                            </div>
                            <!-- Format boolean values -->
                            <div v-else-if="typeof props.row[field.field] === 'boolean'" class="field-value">
                              <q-icon :name="props.row[field.field] ? 'check_circle' : 'cancel'"
                                     :color="props.row[field.field] ? 'positive' : 'negative'" />
                              {{ props.row[field.field] ? 'Yes' : 'No' }}
                            </div>
                            <!-- Default formatting -->
                            <div v-else class="field-value">
                              {{ props.row[field.field] }}
                            </div>
                          </q-card-section>
                        </q-card>
                      </template>
                    </div>
                  </div>

                  <!-- Status History Tab Content -->
                  <div v-show="expandedTab === 'history'" class="tab-content">
                    <div class="text-h6 q-mb-md">Status History</div>
                    <div v-if="statusHistory[props.row.id]">
                      <q-list bordered separator>
                        <q-item v-for="history in statusHistory[props.row.id]" :key="history.id">
                          <q-item-section>
                            <q-item-label>
                              <q-badge :color="getStatusColor(history.new_status)" class="q-mr-sm">
                                {{ history.new_status }}
                              </q-badge>
                              Changed from
                              <q-badge :color="getStatusColor(history.previous_status)" class="q-mx-sm">
                                {{ history.previous_status }}
                              </q-badge>
                              by {{ history.changed_by_name }}
                            </q-item-label>
                            <q-item-label caption>
                              {{ formatDate(history.change_date) }}
                            </q-item-label>
                            <q-item-label v-if="history.notes" caption class="q-mt-xs">
                              <strong>Notes:</strong> {{ history.notes }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                    <div v-else-if="loadingHistory[props.row.id]" class="text-center q-pa-md">
                      <q-spinner color="primary" size="2em" />
                      <div class="q-mt-sm">Loading status history...</div>
                    </div>
                    <div v-else class="text-center q-pa-md">
                      <q-btn color="primary" label="Load Status History" @click="loadStatusHistory(props.row.id)" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-td>
        </q-tr>
      </template>

      <template v-slot:no-data>
        <div class="full-width row flex-center q-gutter-sm q-pa-md">
          <q-icon name="search_off" size="2em" color="grey" />
          <span class="text-grey">{{ t('templates.noTemplatesFound') }}</span>
        </div>
      </template>

      <!-- Custom bottom slot to override "Records per page" text -->
      <template v-slot:bottom>
        <div class="q-table__bottom row items-center no-border-top full-width">
          <!-- Left side - selection info -->
          <div class="q-table__control" v-if="selectedRows.length > 0">
            <div class="q-table__bottom-item">
              {{ t('templates.recordsSelected', { count: selectedRows.length }) }}
            </div>
          </div>

          <!-- Spacer to push pagination to the right -->
          <div class="col"></div>

          <!-- Right side - pagination controls in one line -->
          <div class="row items-center no-wrap">
            <!-- Per page selector -->
            <div class="q-table__control">
              <div class="q-table__bottom-item row items-center no-wrap">
                <span class="q-mr-sm">{{ t('templates.recordsPerPage') }}</span>
                <q-select
                  v-model="tablePagination.rowsPerPage"
                  :options="[10, 20, 50, 100]"
                  dense
                  borderless
                  emit-value
                  map-options
                  options-dense
                  style="min-width: 50px; display: inline-block;"
                />
              </div>
            </div>

            <!-- Pagination buttons -->
            <div class="q-table__control q-ml-md">
              <div class="q-table__bottom-item">
                <q-pagination
                  v-model="tablePagination.page"
                  :max="Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage)"
                  :max-pages="$q.screen.lt.sm ? 3 : 6"
                  direction-links
                  boundary-links
                />
              </div>
            </div>
          </div>

          <!-- Mobile-only pagination -->
          <div class="mobile-only-pagination" style="width: 100%; text-align: center; margin-top: 10px; display: none; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
            <div style="margin-bottom: 8px;">
              <span style="margin-right: 8px;">{{ t('templates.recordsPerPage') }}</span>
              <q-select
                v-model="tablePagination.rowsPerPage"
                :options="[10, 20, 50, 100]"
                dense
                borderless
                emit-value
                map-options
                options-dense
                style="min-width: 50px; display: inline-block;"
                :prefix="t('templates.by') + ':'"
              />
            </div>
            <div>
              <q-pagination
                v-model="tablePagination.page"
                :max="Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage)"
                :max-pages="3"
                direction-links
                boundary-links
                style="display: inline-flex;"
              />
            </div>
          </div>
        </div>
      </template>
    </q-table>

    <!-- Mobile-only pagination outside the table -->
    <div class="mobile-pagination-container">
      <div class="q-px-sm q-py-xs">
        <div class="row justify-between items-center">
          <!-- Records per page selector - ultra compact -->
          <div class="row items-center no-wrap">
            <q-select
              v-model="tablePagination.rowsPerPage"
              :options="[10, 20, 50, 100]"
              dense
              borderless
              emit-value
              map-options
              options-dense
              style="min-width: 40px; display: inline-block;"
              :prefix="t('templates.by') + ':'"
            />
          </div>

          <!-- Simplified pagination - only navigation buttons -->
          <div class="row items-center" style="display: flex; align-items: center;">
            <!-- Custom pagination implementation -->
            <div class="custom-pagination row items-center">
              <!-- First page button -->
              <q-btn
                flat
                round
                dense
                icon="first_page"
                :disable="tablePagination.page === 1"
                @click="tablePagination.page = 1"
                class="custom-pagination-btn"
              />

              <!-- Previous page button -->
              <q-btn
                flat
                round
                dense
                icon="chevron_left"
                :disable="tablePagination.page === 1"
                @click="tablePagination.page--"
                class="custom-pagination-btn"
              />

              <!-- Page display -->
              <div class="q-px-md text-center">
                {{ tablePagination.page }} / {{ Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage) || 1 }}
              </div>

              <!-- Next page button -->
              <q-btn
                flat
                round
                dense
                icon="chevron_right"
                :disable="tablePagination.page >= Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage)"
                @click="tablePagination.page++"
                class="custom-pagination-btn"
              />

              <!-- Last page button -->
              <q-btn
                flat
                round
                dense
                icon="last_page"
                :disable="tablePagination.page >= Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage)"
                @click="tablePagination.page = Math.ceil(tablePagination.rowsNumber / tablePagination.rowsPerPage) || 1"
                class="custom-pagination-btn"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import { date, useQuasar } from 'quasar';
import { useFilterStore } from 'src/stores/filterStore';
import PromptElementSelector from './PromptElementSelector.vue';
import PromptElementsEditor from './PromptElementsEditor.vue';
import type { PromptElement, PromptElementType } from 'src/services/promptElementsService';
import { notificationService } from 'src/services/notificationService';
import { useTemplateService } from 'src/services/templateService';
import { useAuthStore } from 'src/stores/auth';
import type { TemplateStatusHistory } from 'src/types/Template';
import { notify } from 'src/boot/notifications';
import { useI18n } from 'vue-i18n';
import { usePromptElementsService } from 'src/services/promptElementsService';

// Define the Template interface
interface Template {
  id: number;
  name: string;
  description: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  prompt_elements: Array<{
    id: number;
    name?: string;
    value?: string;
    type_name?: string;
    category?: string;
    [key: string]: unknown;
  }>;
  collections: Array<{
    id: number;
    name: string;
    [key: string]: unknown;
  }>;
  products: Array<{
    id: number;
    name: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

// Props
const props = defineProps<{
  templates: Template[];
  loading: boolean;
  pagination: {
    limit: number;
    offset: number;
    total: number;
  };
  sorting: {
    sort_by: string;
    sort_desc: boolean;
  };
}>();

// Emits
const emit = defineEmits<{
  (e: 'request', props: { pagination: { page: number, rowsPerPage: number, sortBy: string, descending: boolean } }): void;
  (e: 'view', template: Template): void;
  (e: 'selection-change', selected: Template[]): void;
}>();

// Initialize Quasar
const $q = useQuasar();

// Initialize template service
const templateService = useTemplateService();

// Initialize prompt elements service
const promptElementsService = usePromptElementsService();

// Initialize auth store
const authStore = useAuthStore();

// Initialize i18n
const { t } = useI18n();

// State
const selectedRows = ref<Template[]>([]);
const expandedRows = ref<number[]>([]);
const expandedTab = ref('elements');
const lastSelectedRowIndex = ref<number | null>(null); // Track the last selected row for shift-click
const shiftKeyPressed = ref(false); // Track if shift key is pressed

// Template elements data for new editor
const templateElementsData = ref<Record<number, Array<{ type: PromptElementType; values: PromptElement[] }>>>({});

// Watch for row expansion to load template elements data
watch(expandedRows, async (newExpandedRows, oldExpandedRows) => {
  // Find newly expanded rows
  const newlyExpanded = newExpandedRows.filter(id => !oldExpandedRows?.includes(id));

  // Load template elements data for newly expanded rows if Elements tab is active
  for (const templateId of newlyExpanded) {
    if (expandedTab.value === 'elements' && !templateElementsData.value[templateId]) {
      await loadTemplateElementsData(templateId);
    }
  }
});

// Drag selection state
const isDragSelecting = ref(false);
const altKeyPressed = ref(false);
const startRowIndex = ref<number | null>(null);

// Status history state
const statusHistory = ref<Record<number, TemplateStatusHistory[]>>({});
const loadingHistory = ref<Record<number, boolean>>({});

// Watch for selection changes
watch(selectedRows, (newSelection: Template[]) => {
  emit('selection-change', newSelection);
});

// Event handlers for keyboard events
function handleKeyDown(e: KeyboardEvent) {
  if (e.key === 'Shift') {
    shiftKeyPressed.value = true;
  } else if (e.key === 'Alt') {
    e.preventDefault(); // Prevent browser's default Alt behavior
    altKeyPressed.value = true;
    document.body.classList.add('drag-select-mode');
  }
}

function handleKeyUp(e: KeyboardEvent) {
  if (e.key === 'Shift') {
    shiftKeyPressed.value = false;
  } else if (e.key === 'Alt') {
    altKeyPressed.value = false;
    isDragSelecting.value = false;
    document.body.classList.remove('drag-select-mode');
  }
}

// Handle document mouseup to end drag selection
function handleDocumentMouseUp() {
  if (isDragSelecting.value) {
    isDragSelecting.value = false;
    document.body.classList.remove('drag-selecting');
  }
}

// Add event listeners
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('keyup', handleKeyUp);
  document.addEventListener('mouseup', handleDocumentMouseUp);
});

// Remove event listeners on unmount
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('keyup', handleKeyUp);
  document.removeEventListener('mouseup', handleDocumentMouseUp);
});

// Table pagination (for q-table)
const tablePagination = ref({
  rowsPerPage: props.pagination.limit,
  page: Math.floor(props.pagination.offset / props.pagination.limit) + 1,
  rowsNumber: props.pagination.total,
  sortBy: props.sorting.sort_by,
  descending: props.sorting.sort_desc
});

// Update pagination when props change
watch(() => props.pagination, (newPagination) => {
  tablePagination.value = {
    ...tablePagination.value,
    rowsPerPage: newPagination.limit,
    page: Math.floor(newPagination.offset / newPagination.limit) + 1,
    rowsNumber: newPagination.total
  };
}, { deep: true });

// Watch for changes to rowsPerPage from any of the pagination dropdowns
watch(() => tablePagination.value.rowsPerPage, (newRowsPerPage, oldRowsPerPage) => {
  // Only emit if the value actually changed and it's not the initial setup
  if (newRowsPerPage !== oldRowsPerPage && oldRowsPerPage !== undefined) {
    console.log('Page size changed from', oldRowsPerPage, 'to', newRowsPerPage);

    // Reset to page 1 when changing page size
    tablePagination.value.page = 1;

    // Emit request event with updated pagination
    emit('request', {
      pagination: tablePagination.value
    });
  }
});

// Watch for changes to page from any of the pagination components
watch(() => tablePagination.value.page, (newPage, oldPage) => {
  // Only emit if the value actually changed and it's not the initial setup
  if (newPage !== oldPage && oldPage !== undefined) {
    console.log('Page changed from', oldPage, 'to', newPage);

    // Emit request event with updated pagination
    emit('request', {
      pagination: tablePagination.value
    });
  }
});

// Debug templates when they change
watch(() => props.templates, (newTemplates) => {
  if (newTemplates.length > 0) {
    logTemplateData();
  }
}, { immediate: true });

// Update sorting when props change
watch(() => props.sorting, (newSorting) => {
  tablePagination.value = {
    ...tablePagination.value,
    sortBy: newSorting.sort_by,
    descending: newSorting.sort_desc
  };
}, { deep: true });

// Get the filter store
const filterStore = useFilterStore();

// Table columns - use only main visible columns from the filter store
const tableColumns = computed(() => {
  // Get only main visible columns from the filter store
  const visibleColumns = filterStore.mainTableColumns.filter(col => col.visible);

  // Map to QTableColumn format with fully responsive widths
  const columns = visibleColumns.map(col => {
    // Define column properties based on column type
    let style = '';
    let classes = '';

    // Set specific styles for different column types
    if (col.id === 'status') {
      style = 'width: 100px;'; // Reduced width for status column
      classes = 'status-column';
    } else if (col.id === 'name') {
      style = 'width: 25%;'; // Name takes 25% of space
    } else if (col.id === 'description') {
      style = 'width: 40%;'; // Description takes 40% of space
    }

    return {
      name: col.id,
      label: translateColumnName(col.name),
      field: col.field,
      sortable: true,
      align: col.id === 'status' ? 'center' as const : 'left' as const,
      style: style,
      classes: classes
    };
  });

  // Add actions column
  columns.push({
    name: 'actions',
    label: t('columns.actions'),
    field: 'actions',
    sortable: false,
    align: 'center' as const,
    style: 'width: 33px;',
    classes: 'actions-column'
  });

  return columns;
});

// Detail fields for the expanded view - use only expanded visible columns from the filter store
const detailFields = computed(() => {
  // Get only expanded visible columns from the filter store
  const visibleColumns = filterStore.expandedViewColumns.filter(col => col.visible);

  // Map to the format needed for detail fields
  return visibleColumns.map(col => ({
    name: col.id,
    label: translateColumnName(col.name),
    field: col.field
  }));
});

// Computed properties for batch actions
// Check if all selected templates have the same status
const selectedTemplatesHaveSameStatus = computed(() => {
  if (selectedRows.value.length === 0) return false;

  const firstTemplate = selectedRows.value[0];
  if (!firstTemplate || !firstTemplate.status) return false;

  const firstStatus = firstTemplate.status;
  return selectedRows.value.every(template => template && template.status === firstStatus);
});

// Get the common status of selected templates (if they all have the same status)
const selectedTemplatesStatus = computed(() => {
  if (!selectedTemplatesHaveSameStatus.value || selectedRows.value.length === 0) return '';

  const firstTemplate = selectedRows.value[0];
  return firstTemplate && firstTemplate.status ? firstTemplate.status : '';
});

// Check if batch submit for approval is available (all templates must be in draft status)
const canBatchSubmitForApproval = computed(() => {
  return selectedTemplatesHaveSameStatus.value &&
    selectedTemplatesStatus.value === 'draft' &&
    hasRole('designer');
});

// Check if batch approve is available (all templates must be in draft or pending status)
const canBatchApprove = computed(() => {
  return selectedTemplatesHaveSameStatus.value &&
    ['draft', 'pending'].includes(selectedTemplatesStatus.value) &&
    hasRole('admin');
});

// Check if batch reject is available (all templates must be in pending status)
const canBatchReject = computed(() => {
  return selectedTemplatesHaveSameStatus.value &&
    selectedTemplatesStatus.value === 'pending' &&
    hasRole('admin');
});

// Check if batch archive is available (all templates must be in active status)
const canBatchArchive = computed(() => {
  return selectedTemplatesHaveSameStatus.value &&
    selectedTemplatesStatus.value === 'active' &&
    hasRole('admin');
});

// Methods
function onRequest(props: { pagination: { page: number, rowsPerPage: number, sortBy: string, descending: boolean } }) {
  console.log('onRequest called with pagination:', props.pagination);
  emit('request', props);
}

// Debug function to log template data
function logTemplateData() {
  if (props.templates.length > 0) {
    const template = props.templates[0];
    console.log('Template data structure:', {
      id: template?.id,
      name: template?.name,
      prompt_elements: template?.prompt_elements,
      collections: template?.collections,
      products: template?.products
    });
  }
}

// View template function removed

function getStatusColor(status: string): string {
  switch (status) {
    case 'draft':
      return 'blue';
    case 'pending':
      return 'orange';
    case 'active':
    case 'published':
      return 'green';
    case 'archived':
      return 'grey';
    default:
      return 'grey';
  }
}

function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  return date.formatDate(dateStr, 'YYYY-MM-DD HH:mm');
}

/**
 * Translate column name using the column name mapping
 */
function translateColumnName(name: string): string {
  // Map common column names to translation keys
  const columnMap: Record<string, string> = {
    'Name': 'columns.name',
    'Description': 'columns.description',
    'Status': 'columns.status',
    'Created Date': 'columns.createdDate',
    'Updated Date': 'columns.updatedDate',
    'Designer': 'columns.designer',
    'Type': 'columns.type',
    'Category': 'columns.category',
    'Value': 'columns.value',
    'Actions': 'columns.actions'
  };

  // If the column name is in our map, translate it, otherwise return the original name
  return columnMap[name] ? t(columnMap[name]) : name;
}

// Handle checkbox change with shift-click support
function handleCheckboxChange(isChecked: boolean, rowIndex: number) {
  // If we're in drag selection mode, don't do anything here
  // The selection will be handled by the drag selection handlers
  if (isDragSelecting.value) return;

  // Get the shift key state - we'll use a global variable since we can't access the event directly
  const isShiftKeyPressed = shiftKeyPressed.value;

  // If shift key is pressed and we have a previous selection
  if (isShiftKeyPressed && lastSelectedRowIndex.value !== null) {
    // Get the range of rows to select
    const startIndex = Math.min(lastSelectedRowIndex.value, rowIndex);
    const endIndex = Math.max(lastSelectedRowIndex.value, rowIndex);

    // Select all rows in the range
    for (let i = startIndex; i <= endIndex; i++) {
      const rowInRange = props.templates[i];
      if (rowInRange) {
        // Check if this row is already in the selection
        const alreadySelected = selectedRows.value.some(
          selectedRow => selectedRow.id === rowInRange.id
        );

        // If not already selected and we're checking, add it
        if (!alreadySelected && isChecked) {
          selectedRows.value.push(rowInRange);
        }
      }
    }
  }

  // Update the last selected row index
  lastSelectedRowIndex.value = rowIndex;
}

// Handle checkbox mousedown for drag selection
function handleCheckboxMouseDown(event: MouseEvent, rowIndex: number) {
  if (altKeyPressed.value) {
    // Start drag selection
    isDragSelecting.value = true;
    startRowIndex.value = rowIndex;
    document.body.classList.add('drag-selecting');

    // Select or deselect the current row
    const row = props.templates[rowIndex];
    if (row) {
      // Check if this row is already selected
      const isSelected = selectedRows.value.some(selected => selected.id === row.id);

      // Toggle selection for this row
      if (!isSelected) {
        // Add to selection
        selectedRows.value.push(row);
      } else {
        // Remove from selection
        selectedRows.value = selectedRows.value.filter(selected => selected.id !== row.id);
      }
    }

    // Prevent default behavior
    event.preventDefault();
  }
}

// Handle checkbox mouseover for drag selection
function handleCheckboxMouseOver(rowIndex: number) {
  if (isDragSelecting.value && startRowIndex.value !== null) {
    // Get the row
    const row = props.templates[rowIndex];
    if (row) {
      // Check if this row is already selected
      const isSelected = selectedRows.value.some(selected => selected.id === row.id);

      // If not already selected, add it
      if (!isSelected) {
        selectedRows.value.push(row);
      }
    }
  }
}

// Row click handler removed - we only want checkbox selection

// Handle elements update from the prompt elements selector
async function onElementsUpdate(elements: Array<PromptElement & { [key: string]: unknown }>, templateId: number) {
  console.log('Elements updated for template ID:', templateId, elements);

  // Show notification
  await notificationService.notify({
    type: 'positive',
    message: 'Prompt elements updated',
    position: 'bottom-right',
    timeout: 2000
  });

  // Find the template in the templates array
  const templateIndex = props.templates.findIndex(t => t.id === templateId);
  if (templateIndex !== -1) {
    // Get the template from the templates array
    const template = props.templates[templateIndex];

    // Check if the template exists
    if (template) {
      // Log the updated elements
      console.log('Updated elements for template ID:', templateId, elements);

      // Instead of creating a new template object, we'll just log the changes
      // This avoids TypeScript errors with the Template interface
      console.log('Template that would be updated:', template.id);
    } else {
      console.error('Template not found with ID:', templateId);
    }

    // Update the local templates array
    // Note: This is a workaround since we can't directly mutate props
    // In a real application, you would update the state in a store or parent component
    console.log('Updated template at index', templateIndex, 'with new elements');
  }

  // We don't need to emit a request event to refresh the table data
  // This would cause the entire table to reload
  // Instead, we'll just update the local data
}

// Handle new elements editor data update
async function onNewElementsUpdate(data: Array<{ type: PromptElementType; values: PromptElement[] }>, templateId: number) {
  console.log('New elements editor data updated for template ID:', templateId, data);

  // Show notification
  await notificationService.notify({
    type: 'positive',
    message: 'New prompt elements updated',
    position: 'bottom-right',
    timeout: 2000
  });
}

// Handle new elements editor prompt update
function onPromptUpdate(prompt: string, templateId: number) {
  console.log('New prompt generated for template ID:', templateId, prompt);
}

// Handle tab change
async function onTabChange(tab: string, templateId: number) {
  console.log('🔄 Tab changed to:', tab, 'for template:', templateId);

  if (tab === 'elements') {
    // Load template elements data when Elements tab is opened
    if (!templateElementsData.value[templateId]) {
      await loadTemplateElementsData(templateId);
    }
  } else if (tab === 'history') {
    // Load status history when History tab is opened (existing functionality)
    if (!statusHistory.value[templateId]) {
      await loadStatusHistory(templateId);
    }
  }
}

// Load template elements data for new editor
async function loadTemplateElementsData(templateId: number) {
  try {
    const { types } = await promptElementsService.getTemplateElementsWithTypes(templateId);

    // Convert to the format expected by PromptElementsEditor
    const elementsData = types.map(type => ({
      type: {
        id: type.id,
        name: type.name,
        description: type.description,
        is_array: false, // Default value
        is_required: false // Default value
      } as PromptElementType,
      values: type.elements.map(element => ({
        id: element.id,
        type_id: type.id,
        value: element.value,
        description: element.description,
        is_system: true,
        designer_id: null,
        created_at: '',
        updated_at: ''
      } as PromptElement))
    }));

    templateElementsData.value[templateId] = elementsData;

    return elementsData;
  } catch (error) {
    console.error('Error loading template elements data:', error);
    return [];
  }
}

// Check if user has a specific role
function hasRole(role: 'designer' | 'admin' | 'super_admin'): boolean {
  // Check if the user has the specified role in their roles array
  if (!authStore.state.user || !authStore.state.user.roles) {
    return false;
  }

  // The roles array contains the role names directly
  return authStore.state.user.roles.some(
    userRole => userRole.toLowerCase() === role.toLowerCase()
  );
}

// Update template status
async function updateTemplateStatus(template: Template, newStatus: 'draft' | 'pending' | 'active' | 'archived') {
  try {
    // Call the template service function to update status
    const success = await templateService.updateTemplateStatus(
      template.id,
      newStatus,
      // Optional notes could be added here
      `Status changed from ${template.status} to ${newStatus}`
    );

    if (success) {
      // Show notification based on status change
      const statusMessages = {
        'draft': 'Template returned to draft',
        'pending': 'Template submitted for approval',
        'active': 'Template approved and activated',
        'archived': 'Template archived'
      };

      void notify({
        type: 'positive',
        message: statusMessages[newStatus] || `Status updated to ${newStatus}`,
        position: 'bottom-right',
        timeout: 2000
      });

      // Update the template status in the current row without reloading the table
      const oldStatus = template.status;
      template.status = newStatus;

      // Also update published field if needed
      if (newStatus === 'active') {
        template.published = true;
      } else if (oldStatus === 'active') {
        // If it was active before and now it's something else, set published to false
        template.published = false;
      }

      // If the status history tab is open and we have already loaded history for this template,
      // reload it to show the new status change
      if (expandedTab.value === 'history' && statusHistory.value[template.id]) {
        await loadStatusHistory(template.id);
      }
    } else {
      void notify({
        type: 'negative',
        message: 'Failed to update template status',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  } catch (error) {
    console.error('Error updating template status:', error);
    void notify({
      type: 'negative',
      message: 'Error updating template status',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}

// Copy template to create a new draft
async function copyTemplate(template: Template) {
  try {
    const newTemplateId = await templateService.copyTemplate(template.id);

    if (newTemplateId) {
      void notify({
        type: 'positive',
        message: 'Template copied successfully',
        position: 'bottom-right',
        timeout: 2000
      });

      // Emit a request event to refresh the table data
      emit('request', {
        pagination: tablePagination.value
      });
    } else {
      void notify({
        type: 'negative',
        message: 'Failed to copy template',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  } catch (error) {
    console.error('Error copying template:', error);
    void notify({
      type: 'negative',
      message: 'Error copying template',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}

// Load template status history
async function loadStatusHistory(templateId: number) {
  try {
    // Set loading state for this template
    loadingHistory.value = { ...loadingHistory.value, [templateId]: true };

    // Call the template service function to get status history
    const history = await templateService.getTemplateStatusHistory(templateId);

    // Update the status history state
    statusHistory.value = { ...statusHistory.value, [templateId]: history };
  } catch (error) {
    console.error('Error loading template status history:', error);
    void notify({
      type: 'negative',
      message: 'Error loading template status history',
      position: 'bottom-right',
      timeout: 3000
    });
  } finally {
    // Clear loading state for this template
    loadingHistory.value = { ...loadingHistory.value, [templateId]: false };
  }
}

// Clear selection
function clearSelection() {
  selectedRows.value = [];
}

// Batch update status for all selected templates
async function batchUpdateStatus(newStatus: 'draft' | 'pending' | 'active' | 'archived') {
  try {
    // Use the notify function that respects user preferences
    void notify({
      type: 'ongoing',
      message: `Updating ${selectedRows.value.length} templates...`,
      position: 'bottom-right',
      timeout: 2000 // Set a reasonable timeout that should cover most operations
    });

    // Process all selected templates in parallel
    const results = await Promise.all(
      selectedRows.value.map(template =>
        templateService.updateTemplateStatus(
          template.id,
          newStatus,
          `Batch update: Status changed from ${template.status} to ${newStatus}`
        )
      )
    );

    // Count successful updates
    const successCount = results.filter(Boolean).length;

    // Show the success notification using the notify function that respects user preferences
    void notify({
      type: 'positive',
      message: `Updated ${successCount} of ${selectedRows.value.length} templates`,
      position: 'bottom-right',
      timeout: 3000
    });

    // Update the status in the local data without reloading
    if (successCount > 0) {
      selectedRows.value.forEach(template => {
        template.status = newStatus;

        // Also update published field if needed
        if (newStatus === 'active') {
          template.published = true;
        } else if (template.status === 'active') {
          template.published = false;
        }
      });
    }

    // Clear selection after successful operation
    clearSelection();
  } catch (error) {
    console.error('Error performing batch update:', error);
    void notify({
      type: 'negative',
      message: 'Failed to update templates',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}

// Confirm batch archive action
function confirmBatchArchive() {
  $q.dialog({
    title: 'Confirm Archive',
    message: `Are you sure you want to archive ${selectedRows.value.length} templates?`,
    cancel: true,
    persistent: true
  }).onOk(() => {
    void batchUpdateStatus('archived');
  });
}

// Batch copy templates to create new drafts
async function batchCopyTemplates() {
  try {
    // Show loading notification with a reasonable timeout
    void notify({
      type: 'ongoing',
      message: `Copying ${selectedRows.value.length} templates...`,
      position: 'bottom-right',
      timeout: 2000 // Set a reasonable timeout that should cover most operations
    });

    // Process all selected templates in parallel
    const results = await Promise.all(
      selectedRows.value.map(template =>
        templateService.copyTemplate(template.id)
      )
    );

    // Count successful copies
    const successCount = results.filter(id => id !== null).length;

    // Show success notification
    void notify({
      type: 'positive',
      message: `Copied ${successCount} of ${selectedRows.value.length} templates`,
      position: 'bottom-right',
      timeout: 3000
    });

    // Refresh the table to show the new templates
    if (successCount > 0) {
      emit('request', {
        pagination: tablePagination.value
      });
    }

    // Clear selection after successful operation
    clearSelection();
  } catch (error) {
    console.error('Error performing batch copy:', error);
    void notify({
      type: 'negative',
      message: 'Failed to copy templates',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}

// Expose methods to parent component
defineExpose({
  selectRowsByIds: (ids: number[]) => {
    // Find templates with the given IDs
    const templatesToSelect = props.templates.filter(template => ids.includes(template.id));
    selectedRows.value = templatesToSelect;
  },
  clearSelection
});
</script>

<style scoped>
.template-table {
  width: 100%;

  /* Make table fully responsive */
  :deep(.q-table) {
    width: 100%;
    table-layout: auto; /* Allow the table to adjust column widths based on content */
  }

  /* Reduce row height */
  :deep(.q-table tbody td) {
    padding-top: 4px;
    padding-bottom: 4px;
    height: 40px; /* Fixed height for rows */
    white-space: normal; /* Allow text to wrap */
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Ensure all table headers have consistent styling */
  :deep(.q-table thead tr th) {
    text-align: left;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    height: 48px !important;
    line-height: 1.2 !important;
  }

  /* Center align the status column header */
  :deep(.q-table thead tr th.status-column) {
    text-align: center !important;
  }

  /* Last column styling */
  :deep(.q-table thead tr th:last-child) {
    font-size: 14px !important; /* Keep consistent with other headers */
  }

  /* Force table layout to respect column widths */
  :deep(.q-table) {
    table-layout: fixed !important;
  }

  /* Checkbox column styling - fixed width of 40px */
  .checkbox-column {
    width: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    max-width: 40px !important;
    box-sizing: border-box !important;
  }

  /* Make the checkbox itself smaller */
  :deep(.checkbox-column .q-checkbox) {
    font-size: 0.8em;
    margin: 0;
    padding: 0;
  }

  /* Center the checkbox in its cell */
  :deep(.checkbox-column .row.justify-center) {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  /* Expand button column styling - fixed width of 40px */
  .expand-column {
    width: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    max-width: 40px !important;
    box-sizing: border-box !important;
  }

  /* Make the expand button itself smaller */
  :deep(.expand-column .q-btn) {
    padding: 4px;
    min-height: 24px;
    min-width: 24px;
    font-size: 0.9em;
  }

  /* Center the button in its cell */
  :deep(.expand-column .row.justify-center) {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  /* Override any auto-width classes */
  :deep(.q-table--col-auto-width) {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  /* Actions column styling */
  .actions-column {
    width: 33px !important;
    padding: 0 !important;
    min-width: 33px !important;
    max-width: 33px !important;
    box-sizing: border-box !important;
  }

  /* Make the action button more compact */
  :deep(.actions-column .q-btn) {
    padding: 4px;
    min-height: 24px;
    min-width: 24px;
    font-size: 0.9em;
  }

  /* Status badge styling - ensure proper display */
  :deep(.status-badge) {
    text-transform: capitalize; /* Capitalize the first letter */
    padding: 4px 6px;          /* Reduced padding around the text */
    font-size: 12px;           /* Set font size */
    font-weight: normal;       /* Set font weight */
    border-radius: 4px;        /* Rounded corners */
    display: inline-block;     /* Allow the badge to size naturally */
    min-width: 70px;           /* Reduced minimum width */
    max-width: 90px;           /* Added maximum width */
    text-align: center;        /* Center the text */
    white-space: nowrap;       /* Prevent text wrapping */
  }

  /* Center align the status column content */
  :deep(td:has(.status-badge)) {
    text-align: center !important;
    width: 100px !important;
    max-width: 100px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* Status column header styling */
  :deep(th.status-column) {
    width: 100px !important;
    max-width: 100px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* Selected row styling */
  .selected-row {
    background-color: rgba(0, 0, 255, 0.05);
  }

  /* Expanded content styles */
  .expanded-content {
    transition: all 0.3s ease;

    .q-tabs {
      margin-bottom: 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    /* Tab content styling */
    .tab-content {
      padding: 0;
      background: transparent;
      transition: opacity 0.3s ease;
    }

    /* Responsive tabs styling */
    .responsive-tabs {
      .q-tab {
        padding: 0 12px;
        min-height: 36px;
      }

      /* Override Quasar's default column layout for tab content */
      :deep(.q-tab__content) {
        flex-direction: row !important;
        column-gap: 8px;
      }

      /* Make icon slightly smaller and remove default margin */
      :deep(.q-tab__icon) {
        font-size: 18px;
        margin: 0 !important;
      }

      /* Ensure label appears on same line as icon */
      :deep(.q-tab__label) {
        margin-top: 0 !important;
      }
    }

    /* Details grid layout */
    .details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 12px;
    }

    /* Field card styling */
    .field-card {
      height: 100%;

      .field-value {
        font-size: 1rem;
        word-break: break-word;
      }
    }
  }

  /* Mobile-only styles */
  @media (max-width: 599px) {
    .mobile-only {
      display: block;
    }

    .desktop-label {
      :deep(.q-tab__label) {
        display: none;
      }
    }

    /* Mobile pagination styles */
    :deep(.q-table__bottom) {
      flex-direction: column;
      align-items: flex-start;

      .q-table__control {
        margin-bottom: 8px;
      }

      /* Push pagination to the right on mobile */
      .row.items-center {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  /* Desktop-only styles */
  @media (min-width: 600px) {
    .mobile-only {
      display: none;
    }

    /* Ensure records per page text and dropdown are in the same row */
    :deep(.q-table__bottom-item.row) {
      display: flex !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
    }

    /* Align pagination controls */
    :deep(.q-table__bottom .row.items-center.no-wrap) {
      display: flex !important;
      align-items: center !important;
    }
  }

  /* Drag selection styles */
  :deep(.drag-selectable) {
    cursor: crosshair !important;
  }

  /* Style for when Alt key is pressed */
  :global(.drag-select-mode) {
    cursor: crosshair !important;
  }

  /* Style for when actively drag selecting */
  :global(.drag-selecting) {
    user-select: none !important;
  }

  /* Remove top border from bottom section and ensure full width */
  :deep(.no-border-top) {
    border-top: none !important;
  }

  :deep(.full-width) {
    width: 100% !important;
  }

  /* Ensure pagination is visible on mobile */
  :deep(.q-table__bottom) {
    display: flex !important;
    flex-wrap: wrap;
  }

  /* Style pagination container */
  .pagination-container {
    display: flex;
    flex-wrap: wrap;
  }

  .pagination-buttons {
    display: flex;
    justify-content: center;
  }

  /* Mobile pagination container */
  .mobile-pagination-container {
    display: none;
    border-radius: 4px;
    margin-top: 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
  }

  /* Ensure vertical alignment in mobile pagination */
  .mobile-pagination-container .row.items-center {
    display: flex;
    align-items: center;
  }

  /* Fix vertical alignment of select dropdown */
  .mobile-pagination-container .q-select {
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }

  /* Fix pagination button alignment for mobile */
  .mobile-pagination-container .q-pagination .q-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    padding: 0 !important;
    height: 24px !important;
  }

  /* Fix selected page background alignment for mobile */
  .mobile-pagination-container .q-pagination .q-btn__content {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    position: relative !important;
    top: 0 !important;
    transform: none !important;
  }

  /* Mobile pagination buttons styling */
  .mobile-pagination-buttons {
    display: flex;
    align-items: center;

    .q-btn {
      margin: 0 2px;
      padding: 0 !important;
      min-height: 32px;
      height: 32px;
      min-width: 32px;
      width: 32px;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      position: relative !important;
      vertical-align: middle !important;
      line-height: 1 !important;
    }

    /* Target the specific button styles from the HTML */
    .q-btn.q-btn-item.non-selectable.no-outline.q-btn--flat.q-btn--rectangle {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0 !important;
      min-height: 32px !important;
      height: 32px !important;
      min-width: 32px !important;
      width: 32px !important;
      vertical-align: middle !important;
    }

    /* Fix input alignment */
    .q-field__native, .q-field__input {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      height: 24px !important;
      min-height: 24px !important;
      line-height: 24px !important;
    }

    /* Fix input container */
    .q-field__control {
      height: 24px !important;
      min-height: 24px !important;
    }

    /* Fix hover effect alignment */
    .q-focus-helper, .q-ripple {
      height: 100%;
      top: 0;
      bottom: 0;
    }

    /* Fix button content alignment */
    span.q-btn__content {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      padding: 0 !important;
      margin: 0 !important;
      line-height: 1 !important;
      position: relative !important;
      top: 0 !important;
      transform: none !important;
    }

    /* Fix button wrapper alignment */
    .q-btn .q-btn__wrapper {
      padding: 0 !important;
      min-height: 32px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* Fix button icon alignment */
    .q-icon {
      line-height: 1 !important;
      position: relative !important;
      top: 0 !important;
      font-size: 1.2em !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      margin: 0 !important;
    }

    /* Target the material icons specifically */
    .q-icon.material-icons {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      width: 100% !important;
      margin: auto !important;
      padding: 0 !important;
      line-height: 1 !important;
    }

    /* Target the material icons inside pagination buttons specifically */
    .mobile-pagination-buttons .q-btn .q-icon.material-icons {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 24px !important;
      width: 24px !important;
      margin: auto !important;
      padding: 0 !important;
      line-height: 1 !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
    }

    /* Target the specific button class */
    button.q-btn {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0 !important;
    }

    /* Target the row class inside button content */
    .q-btn__content.row {
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
    }

    /* Target the span elements inside button content that hold icons */
    .q-btn__content span {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      line-height: 1 !important;
      position: relative !important;
      top: 0 !important;
      transform: none !important;
    }

    /* Target the specific class structure from the HTML */
    .q-btn__content.text-center.col.items-center.q-anchor--skip.justify-center.row {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      padding: 0 !important;
      margin: 0 !important;
      min-height: 24px !important;
    }

    /* Override inline styles on the pagination buttons */
    .mobile-pagination-buttons .q-btn {
      padding: 0 !important;
      min-width: 32px !important;
      min-height: 32px !important;
      height: 32px !important;
      width: 32px !important;
    }

    /* Make sure the input field has the same height as the buttons */
    .mobile-pagination-buttons .q-field__control-container {
      height: 32px !important;
      min-height: 32px !important;
      max-height: 32px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0 !important;
    }

    /* Target the input field itself */
    .mobile-pagination-buttons .q-field__native {
      height: 32px !important;
      min-height: 32px !important;
      padding: 0 !important;
      line-height: 32px !important;
    }

    /* Target the q-field control */
    .mobile-pagination-buttons .q-field__control {
      height: 32px !important;
      min-height: 32px !important;
      max-height: 32px !important;
      padding: 0 !important;
    }

    /* Target the buttons with inline styles */
    .mobile-pagination-buttons .q-btn[style*="padding: 3px 2px; min-width: 0px; min-height: 0px;"] {
      padding: 0 !important;
      min-width: 32px !important;
      min-height: 32px !important;
      height: 32px !important;
      width: 32px !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* Target the icon inside the buttons with inline styles */
    .mobile-pagination-buttons .q-btn[style*="padding: 3px 2px; min-width: 0px; min-height: 0px;"] .q-icon {
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      margin: 0 !important;
    }

    /* Target the button content inside the buttons with inline styles */
    .mobile-pagination-buttons .q-btn[style*="padding: 3px 2px; min-width: 0px; min-height: 0px;"] .q-btn__content {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      height: 100% !important;
      width: 100% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* Custom pagination styles */
    .custom-pagination {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .custom-pagination-btn {
      width: 32px;
      height: 32px;
      min-height: 32px;
      min-width: 32px;
      padding: 0;
      margin: 0 2px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .custom-pagination-btn .q-icon {
      font-size: 1.2em;
    }

    /* Ensure all elements in the pagination container have the same height */
    .mobile-pagination-buttons .q-pagination__content > * {
      height: 32px !important;
      min-height: 32px !important;
      max-height: 32px !important;
      line-height: 32px !important;
    }

    /* Ensure the pagination container itself is properly aligned */
    .mobile-pagination-buttons .q-pagination__content {
      height: 32px !important;
      min-height: 32px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* Target the button content in mobile pagination */
    .mobile-pagination-buttons .q-btn .q-btn__content {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      padding: 0 !important;
      margin: 0 !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
    }

    /* Specifically target the icon elements inside pagination buttons */
    .q-pagination .q-btn .q-icon {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      width: 100% !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      margin: auto !important;
      line-height: 1 !important;
      transform: none !important;
    }

    /* Ensure all elements are vertically centered */
    .q-pagination__content {
      display: flex;
      align-items: center;
      height: 32px;
    }

    /* Make the page display read-only */
    .q-field--readonly {
      pointer-events: none !important;

      .q-field__native {
        opacity: 1 !important;
        color: inherit !important;
      }
    }

    /* Center the page display text */
    .q-field__control-container {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      height: 24px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  /* Dark mode support */
  .body--light .mobile-pagination-container {
    background-color: #f5f5f5;
  }

  .body--dark .mobile-pagination-container {
    background-color: #1d1d1d;
    border-color: rgba(255, 255, 255, 0.12);
  }

  /* Mobile styles */
  @media (max-width: 599px) {
    /* Show mobile pagination container */
    .mobile-pagination-container {
      display: block;
    }

    /* Hide table bottom on mobile */
    :deep(.q-table__bottom) {
      display: none !important;
    }

    /* Make pagination more compact on mobile */
    :deep(.q-pagination .q-btn) {
      padding: 4px !important;
      min-width: 24px !important;
      height: 24px !important;
    }
  }

  /* Add a hint about drag selection */
  :deep(.q-table thead tr th.checkbox-column::after) {
    content: "Hold Alt for drag select";
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 10px;
    color: #999;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  :deep(.q-table thead tr th.checkbox-column:hover::after) {
    opacity: 1;
  }
}
</style>
